# 海尔插座生产装箱扫码拍照系统操作指导书

---

**版本：** V1.0
**编写日期：** 2024年12月17日
**适用范围：** 海尔插座生产线操作人员

---

## 目录

### 第一章 系统概述
1.1 系统简介
1.2 主要功能
1.3 系统界面介绍
1.4 登录与退出

### 第二章 基础数据管理
2.1 用户信息维护
2.1.1 用户列表查看
2.1.2 新增用户
2.1.3 编辑用户信息
2.1.4 删除用户
2.2 产品信息维护
2.2.1 产品列表查看
2.2.2 新增产品
2.2.3 编辑产品信息
2.2.4 删除产品
2.3 生产订单管理
2.3.1 订单列表查看
2.3.2 新增生产订单
2.3.3 编辑订单信息
2.3.4 订单状态管理

### 第三章 生产界面操作
3.1 操作工登录
3.2 订单选择与产品信息
3.3 硬件设备状态监控
3.4 生产模式选择
3.5 质量检测工序
3.6 成品拍照工序
3.7 小盒贴打印工序
3.8 大箱称重与装箱工序
3.9 生产进度监控
3.10 异常处理与恢复

### 第四章 追溯与统计
4.1 生产明细查询
4.2 条码追溯
4.2.1 条码追溯查询
4.2.2 追溯信息解读

### 第五章 常见问题与解决方案
5.1 设备连接问题
5.2 扫码问题
5.3 拍照问题
5.4 打印问题
5.5 数据异常问题

### 第六章 注意事项与安全须知
6.1 操作注意事项
6.2 数据安全
6.3 设备维护

---

## 第一章 系统概述

### 1.1 系统简介

海尔插座生产装箱扫码拍照系统是一套专为海尔插座产品生产线设计的智能化生产管理系统。系统集成了条码扫描、质量检测、产品拍照、自动装盒装箱、重量检测等功能，实现了从原材料到成品包装的全流程数字化管理。

**系统特点：**
- 全流程数字化管理
- 实时质量检测与控制
- 自动化包装与称重
- 完整的生产追溯体系
- 多种生产模式支持

### 1.2 主要功能

**基础数据管理：**
- 用户信息维护：管理系统操作人员信息
- 产品信息维护：管理产品型号、规格等基础信息
- 生产订单管理：管理生产计划和订单信息

**生产过程控制：**
- 质量检测：通过DI信号检测产品质量状态
- 成品拍照：自动拍摄产品照片并添加水印
- 自动装盒：根据设定数量自动装盒并打印标签
- 自动装箱：根据重量自动装箱并打印大箱标签

**数据追溯与统计：**
- 生产明细查询：查询详细的生产数据
- 条码追溯：通过条码查询产品完整生产历程

### 1.3 系统界面介绍

系统采用现代化的Windows桌面应用程序界面，主要包含以下区域：

**标题栏：** 显示系统名称和Logo
**菜单栏：** 包含各功能模块的入口菜单
**工作区域：** 显示当前操作的功能界面
**状态栏：** 显示系统运行状态信息

**主要菜单项：**
- 工作界面：生产操作的主界面
- 追溯与统计：数据查询和追溯功能
- 基础数据管理：系统基础数据维护

### 1.4 登录与退出

**系统启动：**
1. 双击桌面上的系统图标启动程序
2. 系统会自动最大化显示主界面
3. 默认显示工作界面，等待操作工登录

**系统退出：**
1. 点击窗口右上角的关闭按钮
2. 系统会自动保存当前状态并安全退出
3. 如有未完成的操作，系统会提示确认

*【此处插入系统主界面截图】*

---

## 第二章 基础数据管理

基础数据管理是系统正常运行的基础，包括用户信息、产品信息和生产订单的维护。只有管理员权限的用户才能进行基础数据的维护操作。

### 2.1 用户信息维护

用户信息维护功能用于管理系统中的所有操作人员信息，包括用户的基本信息、权限设置等。

#### 2.1.1 用户列表查看

**操作步骤：**
1. 在主菜单中点击"基础数据管理"
2. 在下拉菜单中选择"用户信息维护"
3. 系统打开用户管理界面，显示用户列表

**界面说明：**
- 用户列表以表格形式显示所有用户信息
- 显示字段包括：用户名、姓名、角色、创建时间、状态等
- 支持按用户名、姓名等条件搜索用户
- 列表支持排序和分页显示

*【此处插入用户列表界面截图】*

#### 2.1.2 新增用户

**操作步骤：**
1. 在用户管理界面点击"新增用户"按钮
2. 系统弹出用户信息编辑对话框
3. 填写用户基本信息：
   - **用户名：** 用于登录的唯一标识（必填）
   - **姓名：** 用户的真实姓名（必填）
   - **密码：** 登录密码（必填，至少6位）
   - **确认密码：** 再次输入密码进行确认
   - **角色：** 选择用户角色（操作员/管理员）
   - **状态：** 选择用户状态（启用/禁用）
4. 点击"保存"按钮完成用户创建
5. 系统提示创建成功，用户列表自动刷新

**注意事项：**
- 用户名必须唯一，不能与现有用户重复
- 密码要求至少6位，建议包含字母和数字
- 操作员只能进行生产操作，管理员可以进行所有操作

*【此处插入新增用户对话框截图】*

#### 2.1.3 编辑用户信息

**操作步骤：**
1. 在用户列表中选择要编辑的用户
2. 点击"编辑"按钮或双击用户行
3. 系统弹出用户信息编辑对话框，显示当前用户信息
4. 修改需要更改的信息：
   - 可以修改姓名、角色、状态
   - 用户名不允许修改
   - 如需修改密码，在密码框中输入新密码
5. 点击"保存"按钮保存修改
6. 系统提示修改成功

**注意事项：**
- 不能删除当前登录的用户
- 修改用户角色会影响其操作权限
- 禁用用户后该用户将无法登录系统

*【此处插入编辑用户对话框截图】*

#### 2.1.4 删除用户

**操作步骤：**
1. 在用户列表中选择要删除的用户
2. 点击"删除"按钮
3. 系统弹出确认对话框，提示删除操作不可恢复
4. 点击"确定"按钮确认删除
5. 系统提示删除成功，用户列表自动刷新

**注意事项：**
- 删除操作不可恢复，请谨慎操作
- 不能删除当前登录的用户
- 不能删除系统内置的管理员用户
- 如果用户有相关的生产记录，建议禁用而不是删除

*【此处插入删除确认对话框截图】*

### 2.2 产品信息维护

产品信息维护功能用于管理系统中的产品基础信息，包括产品型号、名称、规格、类别等信息。

#### 2.2.1 产品列表查看

**操作步骤：**
1. 在主菜单中点击"基础数据管理"
2. 在下拉菜单中选择"产品信息维护"
3. 系统打开产品管理界面，显示产品列表

**界面说明：**
- 产品列表以表格形式显示所有产品信息
- 显示字段包括：产品代码、产品名称、类别、规格、创建时间等
- 支持按产品代码、名称等条件搜索产品
- 列表支持排序和分页显示

*【此处插入产品列表界面截图】*

#### 2.2.2 新增产品

**操作步骤：**
1. 在产品管理界面点击"新增产品"按钮
2. 系统弹出产品信息编辑对话框
3. 填写产品基本信息：
   - **产品代码：** 产品的唯一标识码（必填）
   - **产品名称：** 产品的中文名称（必填）
   - **产品类别：** 选择产品类别（插座/开关）
   - **规格型号：** 产品的详细规格信息
   - **包装数量：** 每盒包装的产品数量
   - **备注：** 产品的其他说明信息
4. 点击"保存"按钮完成产品创建
5. 系统提示创建成功，产品列表自动刷新

**注意事项：**
- 产品代码必须唯一，不能与现有产品重复
- 产品类别影响质量检测的方式
- 包装数量用于自动装盒时的数量控制

*【此处插入新增产品对话框截图】*

#### 2.2.3 编辑产品信息

**操作步骤：**
1. 在产品列表中选择要编辑的产品
2. 点击"编辑"按钮或双击产品行
3. 系统弹出产品信息编辑对话框，显示当前产品信息
4. 修改需要更改的信息：
   - 可以修改产品名称、类别、规格、包装数量、备注
   - 产品代码不允许修改
5. 点击"保存"按钮保存修改
6. 系统提示修改成功

**注意事项：**
- 产品代码一旦创建不能修改
- 修改产品类别会影响质量检测流程
- 修改包装数量会影响后续的装盒操作

*【此处插入编辑产品对话框截图】*

#### 2.2.4 删除产品

**操作步骤：**
1. 在产品列表中选择要删除的产品
2. 点击"删除"按钮
3. 系统弹出确认对话框，提示删除操作不可恢复
4. 点击"确定"按钮确认删除
5. 系统提示删除成功，产品列表自动刷新

**注意事项：**
- 删除操作不可恢复，请谨慎操作
- 如果产品已有相关的生产订单或生产记录，不能删除
- 建议在删除前先检查是否有相关数据

*【此处插入删除产品确认对话框截图】*

### 2.3 生产订单管理

生产订单管理功能用于创建和管理生产计划，是生产作业的基础。每个生产订单包含了产品信息、生产数量、交期等重要信息。

#### 2.3.1 订单列表查看

**操作步骤：**
1. 在主菜单中点击"基础数据管理"
2. 在下拉菜单中选择"生产订单管理"
3. 系统打开订单管理界面，显示订单列表

**界面说明：**
- 订单列表以表格形式显示所有生产订单
- 显示字段包括：订单号、产品代码、产品名称、计划数量、完成数量、状态、创建时间等
- 支持按订单号、产品代码等条件搜索订单
- 支持按状态筛选订单（未开始/进行中/已完成）
- 列表支持排序和分页显示

*【此处插入订单列表界面截图】*

#### 2.3.2 新增生产订单

**操作步骤：**
1. 在订单管理界面点击"新增订单"按钮
2. 系统弹出订单信息编辑对话框
3. 填写订单基本信息：
   - **订单号：** 生产订单的唯一编号（必填）
   - **产品代码：** 从下拉列表选择要生产的产品（必填）
   - **计划数量：** 本订单计划生产的产品数量（必填）
   - **交期：** 订单要求的完成日期
   - **备注：** 订单的其他说明信息
4. 点击"保存"按钮完成订单创建
5. 系统提示创建成功，订单列表自动刷新

**注意事项：**
- 订单号必须唯一，不能与现有订单重复
- 产品代码必须是系统中已存在的产品
- 计划数量必须大于0
- 新创建的订单状态为"未开始"

*【此处插入新增订单对话框截图】*

#### 2.3.3 编辑订单信息

**操作步骤：**
1. 在订单列表中选择要编辑的订单
2. 点击"编辑"按钮或双击订单行
3. 系统弹出订单信息编辑对话框，显示当前订单信息
4. 修改需要更改的信息：
   - 可以修改计划数量、交期、备注
   - 订单号和产品代码不允许修改
   - 如果订单已开始生产，计划数量不能小于已完成数量
5. 点击"保存"按钮保存修改
6. 系统提示修改成功

**注意事项：**
- 订单号和产品代码一旦创建不能修改
- 已开始生产的订单，计划数量不能随意减少
- 修改计划数量会影响生产进度的计算

*【此处插入编辑订单对话框截图】*

#### 2.3.4 订单状态管理

**订单状态说明：**
- **未开始：** 订单刚创建，尚未开始生产
- **进行中：** 订单已开始生产，但尚未完成
- **已完成：** 订单生产已完成
- **已暂停：** 订单生产暂时停止

**状态变更操作：**
1. 在订单列表中选择要操作的订单
2. 根据需要点击相应的状态按钮：
   - "开始生产"：将未开始的订单状态改为进行中
   - "暂停生产"：将进行中的订单状态改为已暂停
   - "恢复生产"：将已暂停的订单状态改为进行中
   - "完成订单"：手动将订单状态改为已完成
3. 系统提示状态变更成功

**注意事项：**
- 只有"未开始"状态的订单可以开始生产
- 只有"进行中"状态的订单可以暂停
- 订单完成后不能再次修改状态
- 系统会根据生产进度自动更新订单状态

*【此处插入订单状态管理界面截图】*

---

## 第三章 生产界面操作

生产界面是系统的核心操作界面，集成了整个生产流程的控制功能。操作人员在此界面完成从质量检测到产品装箱的全部生产操作。

### 3.1 操作工登录

在开始生产操作前，操作工必须先登录系统，以确保生产记录的可追溯性。

**操作步骤：**
1. 点击主菜单"工作界面"进入生产操作界面
2. 在左上角"操作工登录"区域输入登录信息：
   - **用户名：** 输入操作工的用户名
   - **密码：** 输入对应的密码
3. 点击"登录"按钮
4. 登录成功后，界面显示操作工姓名和登录时间
5. 登录区域变为显示当前用户信息和"注销"按钮

**登录状态说明：**
- 登录成功后，所有生产操作都会记录当前操作工信息
- 如需更换操作工，点击"注销"按钮后重新登录
- 系统会自动记录操作工的工作时间

**注意事项：**
- 必须使用有效的用户账号登录
- 每个操作工应使用自己的账号，不得共用
- 登录后才能进行生产操作

*【此处插入操作工登录界面截图】*

### 3.2 订单选择与产品信息

操作工登录后，需要选择要生产的订单，系统会自动加载对应的产品信息。

**操作步骤：**
1. 在"订单选择"区域，从下拉列表中选择要生产的订单
2. 选择订单后，系统自动显示以下信息：
   - **产品信息：** 产品代码、名称、类别、规格
   - **订单信息：** 订单号、计划数量、已完成数量
   - **生产进度：** 完成百分比和剩余数量
3. 确认信息无误后，即可开始生产操作

**界面信息说明：**
- **产品代码：** 当前生产产品的唯一标识
- **产品名称：** 产品的中文名称
- **产品类别：** 插座或开关，影响质检流程
- **计划数量：** 本订单总的生产数量
- **完成数量：** 已经完成生产的数量
- **剩余数量：** 还需要生产的数量
- **完成进度：** 以百分比显示的完成情况

**注意事项：**
- 只能选择状态为"未开始"或"进行中"的订单
- 选择订单后不能随意更换，如需更换请联系管理员
- 订单信息会实时更新，反映当前生产进度

*【此处插入订单选择界面截图】*

### 3.3 硬件设备状态监控

系统实时监控各种硬件设备的连接状态，确保生产过程的正常进行。

**设备状态显示：**
在界面右上角的"硬件状态"区域，实时显示各设备状态：

1. **质检扫码枪：**
   - 绿色：设备正常连接
   - 红色：设备连接异常
   - 显示端口信息和连接状态

2. **拍照扫码枪：**
   - 绿色：设备正常连接
   - 红色：设备连接异常
   - 显示端口信息和连接状态

3. **相机设备：**
   - 绿色：相机正常工作
   - 红色：相机连接异常
   - 显示相机型号和状态

4. **电子秤：**
   - 绿色：电子秤正常连接
   - 红色：电子秤连接异常
   - 显示当前重量读数

5. **IO模块：**
   - 绿色：IO模块正常连接
   - 红色：IO模块连接异常
   - 显示DI/DO状态

**异常处理：**
- 如果设备状态显示红色，请检查设备连接
- 可以点击设备状态区域查看详细错误信息
- 设备异常时相关功能将无法正常使用

*【此处插入硬件状态监控界面截图】*

### 3.4 生产模式选择

系统支持三种生产模式，操作人员可根据实际需要选择合适的模式。

**生产模式类型：**

1. **正常生产模式：**
   - 完整的质检+拍照+装盒+装箱流程
   - 严格验证质检结果，不合格产品不能进入后续工序
   - 防止条码重复和漏检
   - 适用于正常生产情况

2. **包装返修模式：**
   - 跳过质检工序，直接进入拍照环节
   - 允许已拍照的产品重新拍照
   - 适用于包装不良品的返修
   - 质检扫码枪在此模式下被忽略

3. **无检测生产模式：**
   - 跳过质检验证，但保持正常的拍照+装盒+装箱流程
   - 不校验质检结果，但仍检查条码重复性
   - 适用于特殊情况下的快速生产
   - 质检扫码枪在此模式下被忽略

**模式切换操作：**
1. 在生产模式选择区域，从下拉列表选择目标模式
2. 系统会检查当前生产状态：
   - 所有队列（拍照、小盒、大箱）必须为空才能切换
   - 如有未完成的产品，系统会提示先完成当前批次
3. 切换成功后，系统显示当前模式和相应的操作说明

**注意事项：**
- 切换模式前必须清空所有生产队列
- 不同模式下的生产数据会被标记，便于后续追溯
- 模式切换会在系统日志中记录

*【此处插入生产模式选择界面截图】*

### 3.5 质量检测工序

质量检测是生产流程的第一步，通过扫码和DI信号检测确保产品质量。

**检测流程：**

1. **扫码操作：**
   - 使用质检扫码枪扫描产品条码
   - 系统验证条码有效性和订单匹配性
   - 防重复扫码：如果正在处理同一条码，重复扫描会被忽略
   - 扫码成功后，界面显示当前检测的条码

2. **质量检测：**
   - 根据产品类别执行不同的检测流程：
     - **插座产品：** 等待DI状态变化，检测插座功能
     - **开关产品：** 检测开关的各个档位功能
   - 系统实时监控DI输入信号
   - 检测过程中界面显示"正在检测..."状态

3. **结果判定：**
   - 系统根据DI信号自动判定产品质量
   - 合格产品：界面显示绿色"✅ 合格"状态
   - 不合格产品：界面显示红色"❌ 不合格"状态
   - 检测结果会自动保存到数据库

**界面信息说明：**
- **当前条码：** 正在检测的产品条码
- **检测状态：** 显示当前检测进度
- **DI状态：** 实时显示各DI点的状态
- **检测历史：** 显示最近的检测记录

**异常处理：**
- 条码无效：系统提示"条码无效"，请检查条码
- 条码重复：系统提示"条码重复"，请检查是否重复扫描
- 检测超时：如果长时间无DI变化，可手动结束检测
- 设备异常：检查IO模块连接状态

**注意事项：**
- 在正常生产模式下，必须通过质检才能进入后续工序
- 不合格产品需要取走，不能进入拍照队列
- 质检记录包含完整的DI状态信息，便于问题追溯

*【此处插入质量检测界面截图】*

### 3.6 成品拍照工序

成品拍照工序对通过质检的产品进行拍照记录，并自动添加水印标识。

**拍照流程：**

1. **扫码触发：**
   - 使用拍照扫码枪扫描产品条码
   - 系统验证条码的质检状态（正常生产模式）
   - 检查条码是否已在拍照队列中
   - 验证通过后，条码进入拍照队列

2. **自动拍照：**
   - 产品进入拍照位置后，系统自动触发拍照
   - 相机拍摄产品照片并保存到指定目录
   - 照片文件名包含条码和时间戳信息
   - 拍照成功后，产品从拍照队列移除并进入小盒队列

3. **水印处理：**
   - 系统后台自动为照片添加水印
   - 水印内容为产品条码，70%透明度，黄色字体
   - 水印大小占图片宽度的80%，居中显示
   - 水印处理在后台进行，不影响生产流程

**界面信息说明：**
- **拍照状态：** 显示当前拍照工序状态
- **待拍照队列：** 显示等待拍照的产品条码列表
- **队列数量：** 显示当前队列中的产品数量
- **最后拍照：** 显示最近拍照的产品信息

**拍照队列管理：**
- 队列按扫码时间顺序排列
- 支持手动删除队列中的条码（右键菜单）
- 队列信息实时更新
- 最多显示100条记录

**异常处理：**
- 条码重复：系统提示"条码已在队列中"
- 质检未通过：系统提示"产品质检不合格"
- 相机异常：检查相机连接状态
- 拍照失败：系统会重试，多次失败需人工处理

**注意事项：**
- 在正常生产和无检测模式下，会检查条码重复性
- 在包装返修模式下，允许重复拍照
- 拍照记录包含操作员信息和生产模式标识
- 照片文件自动备份，支持后续追溯

*【此处插入成品拍照界面截图】*

### 3.7 小盒贴打印工序

小盒贴打印工序将拍照完成的产品按设定数量装盒，并打印小盒标签。

**装盒流程：**

1. **自动装盒：**
   - 系统监控小盒队列中的产品数量
   - 当数量达到产品设定的包装数量时，自动触发装盒
   - 生成唯一的盒号（格式：BOX-日期-序号）
   - 将对应数量的产品分配到该盒号

2. **标签打印：**
   - 系统自动调用BarTender打印服务
   - 打印小盒标签，包含盒号、产品信息、数量等
   - 打印成功后，产品标记为已生产状态
   - 盒号进入大箱队列，等待装箱

3. **数据记录：**
   - 记录装盒时间、操作员、生产模式等信息
   - 更新产品条码的盒号信息
   - 更新订单完成进度

**界面信息说明：**
- **小盒状态：** 显示当前装盒工序状态
- **当前小盒：** 显示正在装盒的产品列表
- **盒内数量：** 显示当前盒中的产品数量
- **目标数量：** 显示每盒应装的产品数量
- **最后盒号：** 显示最近打印的盒号

**装盒队列管理：**
- 显示当前小盒中的所有产品条码
- 支持手动删除队列中的条码
- 支持手动触发打印（当数量不足时）
- 队列信息实时更新

**异常处理：**
- 打印失败：系统会重试打印，并提示错误信息
- 数量不足：可手动触发打印或等待更多产品
- 盒号重复：系统自动生成新的盒号
- 网络异常：检查BarTender服务连接状态

**注意事项：**
- 装盒数量由产品信息中的包装数量决定
- 打印失败的盒号不会进入大箱队列
- 装盒记录包含完整的产品条码列表
- 支持手动调整装盒数量（管理员权限）

*【此处插入小盒贴打印界面截图】*

### 3.8 大箱称重与装箱工序

大箱称重与装箱工序是生产流程的最后环节，将小盒装入大箱并打印大箱标签。

**装箱流程：**

1. **重量监控：**
   - 系统实时监控电子秤的重量变化
   - 当检测到重量从0变为正数时，开始稳定性监控
   - 重量稳定后（变化幅度小于5%，持续5秒），触发自动装箱

2. **自动装箱：**
   - 系统检查大箱队列中是否有待装箱的盒号
   - 生成唯一的箱号（格式：CTN-序号）
   - 将队列中的所有盒号分配到该箱号
   - 记录箱重和包含的产品总数量

3. **标签打印：**
   - 系统自动调用BarTender打印大箱标签
   - 标签包含箱号、重量、盒数、总数量、生产日期等信息
   - 打印成功后，清空大箱队列
   - 等待下一次装箱

**界面信息说明：**
- **当前重量：** 实时显示电子秤读数
- **大箱状态：** 显示装箱工序当前状态
- **大箱队列：** 显示等待装箱的盒号列表
- **队列统计：** 显示盒数和总产品数量
- **最后箱号：** 显示最近打印的箱号信息

**重量监控设置：**
- **稳定阈值：** 重量变化小于5%视为稳定
- **稳定时间：** 重量稳定持续5秒后触发装箱
- **自动打印：** 重量稳定且队列不为空时自动打印
- **重量归零：** 取走货物后重量归零，开始新的监控周期

**手动操作：**
- 支持手动触发装箱打印
- 支持手动调整箱重
- 支持从队列中删除指定盒号
- 支持查看装箱历史记录

**异常处理：**
- 电子秤异常：检查电子秤连接状态
- 打印失败：系统会重试打印
- 队列为空：提示"大箱队列为空，无法装箱"
- 重量异常：检查是否有异物影响称重

**注意事项：**
- 装箱前确保大箱队列中有盒号
- 重量稳定后才会触发自动装箱
- 装箱记录包含完整的盒号列表和重量信息
- 支持重量校准功能（管理员权限）

*【此处插入大箱称重装箱界面截图】*

### 3.9 生产进度监控

系统实时监控和显示生产进度，帮助操作人员了解当前生产状况。

**进度信息显示：**
- **订单进度：** 显示当前订单的完成情况
- **完成数量：** 已完成生产的产品数量
- **剩余数量：** 还需要生产的产品数量
- **完成百分比：** 以百分比和进度条显示完成情况
- **预计完成时间：** 根据当前生产速度估算完成时间

**队列状态监控：**
- **拍照队列：** 显示等待拍照的产品数量
- **小盒队列：** 显示等待装盒的产品数量
- **大箱队列：** 显示等待装箱的盒号数量
- **各队列状态：** 实时更新队列中的产品信息

**生产统计信息：**
- **今日产量：** 显示当天的生产数量
- **小时产量：** 显示每小时的平均产量
- **质检合格率：** 显示质检通过的比例
- **设备运行时间：** 显示设备的运行状态

**异常监控：**
- **设备异常：** 实时监控设备连接状态
- **队列堆积：** 监控队列是否出现异常堆积
- **生产异常：** 监控生产流程是否正常
- **质量异常：** 监控质检合格率是否正常

*【此处插入生产进度监控界面截图】*

### 3.10 异常处理与恢复

生产过程中可能遇到各种异常情况，系统提供了相应的处理和恢复机制。

**常见异常类型：**

1. **质检异常：**
   - 不合格产品检测：系统自动停线，提示取走不合格产品
   - 漏检产品：系统检测到未质检的产品，自动停线
   - 恢复方法：取走异常产品后，点击"恢复线体"按钮

2. **设备连接异常：**
   - 扫码枪断开：检查USB连接和驱动程序
   - 相机断开：检查相机电源和数据线连接
   - 电子秤断开：检查串口连接和波特率设置
   - IO模块断开：检查网络连接和IP地址设置

3. **拍照异常：**
   - 拍照失败：检查相机状态和存储空间
   - 照片保存失败：检查磁盘空间和文件权限
   - 恢复方法：解决问题后，系统会自动重试

4. **打印异常：**
   - 小盒标签打印失败：检查BarTender服务和打印机状态
   - 大箱标签打印失败：检查网络连接和模板文件
   - 恢复方法：解决问题后，可手动重新打印

**恢复操作：**
- **恢复线体：** 清除异常状态，恢复正常生产
- **清空队列：** 清空指定队列中的产品（管理员权限）
- **重新打印：** 重新打印失败的标签
- **手动处理：** 对异常产品进行手动处理

**预防措施：**
- 定期检查设备连接状态
- 保持充足的磁盘存储空间
- 定期清理临时文件和日志
- 及时处理系统提示的异常信息

*【此处插入异常处理界面截图】*

---

## 第四章 追溯与统计

追溯与统计功能提供了完整的生产数据查询和追溯能力，帮助管理人员了解生产情况和产品质量状况。

### 4.1 生产明细查询

生产明细查询功能提供了详细的生产数据查询和统计分析功能。

**功能入口：**
1. 在主菜单中点击"追溯与统计"
2. 在下拉菜单中选择"生产明细查询"
3. 系统打开生产明细查询界面

**查询条件设置：**
- **时间范围：** 设置查询的开始和结束日期
- **订单筛选：** 选择特定的生产订单
- **产品筛选：** 选择特定的产品类型
- **操作员筛选：** 选择特定的操作员
- **生产模式：** 筛选特定生产模式的数据

**查询结果显示：**
- **质检记录：** 显示质检时间、条码、结果、操作员等信息
- **拍照记录：** 显示拍照时间、条码、照片路径等信息
- **装盒记录：** 显示装盒时间、盒号、包含的产品等信息
- **装箱记录：** 显示装箱时间、箱号、重量、包含的盒号等信息

**数据统计功能：**
- **产量统计：** 按时间段统计生产数量
- **质量统计：** 统计质检合格率和不合格原因
- **效率统计：** 统计各工序的生产效率
- **操作员统计：** 统计各操作员的工作量

**数据导出功能：**
- 支持将查询结果导出为Excel文件
- 可选择导出的数据字段
- 支持按条件筛选后导出
- 导出文件包含完整的统计信息

**注意事项：**
- 查询大量数据时可能需要较长时间
- 建议设置合理的时间范围以提高查询效率
- 导出的数据文件较大时请确保磁盘空间充足

*【此处插入生产明细查询界面截图】*

### 4.2 条码追溯

条码追溯功能通过产品条码查询该产品的完整生产历程，实现产品的全流程追溯。

#### 4.2.1 条码追溯查询

**功能入口：**
1. 在主菜单中点击"追溯与统计"
2. 在下拉菜单中选择"条码追溯"
3. 系统打开条码追溯查询界面

**查询操作：**
1. 在条码输入框中输入要查询的产品条码
2. 按回车键或点击"查询"按钮开始查询
3. 系统显示该条码的完整追溯信息
4. 如需查询其他条码，点击"清空"按钮后重新输入

**查询结果验证：**
- 如果条码存在，系统显示完整的追溯信息
- 如果条码不存在，系统提示"未找到该条码的生产记录"
- 查询过程中显示"正在查询..."状态
- 查询失败时显示具体的错误信息

*【此处插入条码追溯查询界面截图】*

#### 4.2.2 追溯信息解读

条码追溯查询结果包含以下几个部分的详细信息：

**1. 基本信息：**
- **条码：** 产品的唯一标识码
- **订单号：** 该产品所属的生产订单
- **产品型号：** 产品的型号代码
- **产品名称：** 产品的中文名称
- **规格：** 产品的详细规格信息
- **生产日期：** 产品的生产日期

**2. 质检信息：**（如果该产品经过质检）
- **质检时间：** 质量检测的具体时间
- **质检结果：** 显示"✅ 合格"或"❌ 不合格"
- **生产模式：** 质检时的生产模式（正常生产/包装返修/无检测生产）
- **DI状态：** 质检时的DI信号状态详情
- **操作员：** 执行质检的操作员姓名
- **备注：** 质检过程中的备注信息

**3. 拍照信息：**（如果该产品已拍照）
- **拍照时间：** 产品拍照的具体时间
- **照片路径：** 照片文件的存储路径
- **生产模式：** 拍照时的生产模式
- **水印状态：** 显示水印处理状态（未处理/已添加/处理失败）
- **操作员：** 执行拍照操作的操作员姓名
- **查看照片：** 点击按钮可直接打开照片文件

**4. 包装信息：**（如果该产品已包装）
- **装盒时间：** 产品装盒的具体时间
- **盒号：** 产品所在的小盒编号
- **装箱时间：** 小盒装入大箱的时间
- **箱号：** 产品最终所在的大箱编号
- **箱重：** 大箱的总重量
- **总数量：** 大箱中包含的产品总数

**5. 生产历程：**
以时间线形式显示产品的完整生产过程：
- **时间：** 各工序的执行时间
- **工序：** 工序名称（质检工序/拍照工序/装盒工序/装箱工序）
- **状态：** 工序执行结果（✅ 完成/❌ 失败）
- **生产模式：** 各工序执行时的生产模式
- **详情：** 工序的详细信息，如操作员、设备状态等

**信息解读要点：**
- 生产历程按时间顺序排列，清晰显示产品流转过程
- 不同生产模式用不同标识区分，便于识别特殊情况
- 所有操作都有操作员记录，确保责任可追溯
- 质检不合格的产品不会有后续的拍照和包装记录
- 包装返修模式的产品可能有多次拍照记录

**注意事项：**
- 只有系统中存在的条码才能查询到信息
- 查询结果的完整性取决于产品实际经历的工序
- 照片查看功能需要照片文件实际存在
- 生产模式信息有助于分析特殊情况下的生产数据

*【此处插入追溯信息详情界面截图】*

---

## 第五章 常见问题与解决方案

本章介绍生产过程中可能遇到的常见问题及其解决方案，帮助操作人员快速处理异常情况。

### 5.1 设备连接问题

**问题1：扫码枪无法扫码**
- **现象：** 扫码枪指示灯不亮，无法读取条码
- **可能原因：** USB连接松动、驱动程序问题、扫码枪故障
- **解决方案：**
  1. 检查USB连接是否牢固
  2. 重新插拔USB接口
  3. 检查设备管理器中的驱动状态
  4. 重启扫码枪或更换设备

**问题2：相机无法拍照**
- **现象：** 界面显示相机连接异常，无法拍摄照片
- **可能原因：** 相机电源问题、数据线连接问题、相机驱动问题
- **解决方案：**
  1. 检查相机电源指示灯是否正常
  2. 检查数据线连接是否牢固
  3. 重启相机设备
  4. 检查相机驱动程序是否正常

**问题3：电子秤读数异常**
- **现象：** 重量显示不准确或无法读取重量
- **可能原因：** 串口连接问题、波特率设置错误、电子秤故障
- **解决方案：**
  1. 检查串口连接线是否正常
  2. 确认波特率设置为9600
  3. 重启电子秤设备
  4. 进行重量校准操作

**问题4：IO模块连接失败**
- **现象：** 无法读取DI状态，质检功能异常
- **可能原因：** 网络连接问题、IP地址冲突、模块故障
- **解决方案：**
  1. 检查网络连接是否正常
  2. 确认IP地址设置正确
  3. 重启IO模块设备
  4. 检查网络防火墙设置

### 5.2 扫码问题

**问题1：条码无法识别**
- **现象：** 扫码枪有反应但系统提示"条码无效"
- **可能原因：** 条码损坏、条码格式不正确、条码不在系统中
- **解决方案：**
  1. 检查条码是否清晰完整
  2. 确认条码格式是否正确
  3. 检查条码是否已录入系统
  4. 清洁条码表面或更换条码

**问题2：重复扫码提示**
- **现象：** 系统提示"条码重复"或"条码已在队列中"
- **可能原因：** 条码已被扫描、手抖重复扫描、系统数据异常
- **解决方案：**
  1. 确认是否为重复扫描
  2. 检查产品是否已在队列中
  3. 等待当前条码处理完成后再扫描
  4. 如有异常可联系管理员清理队列

**问题3：扫码无响应**
- **现象：** 扫码枪正常但系统无任何反应
- **可能原因：** 软件焦点问题、系统繁忙、扫码枪设置问题
- **解决方案：**
  1. 点击软件界面确保焦点正确
  2. 等待系统处理完当前任务
  3. 重启扫码枪设备
  4. 重启软件程序

### 5.3 拍照问题

**问题1：拍照失败**
- **现象：** 系统提示拍照失败，照片无法保存
- **可能原因：** 相机故障、存储空间不足、文件权限问题
- **解决方案：**
  1. 检查相机设备状态
  2. 清理磁盘空间，确保有足够存储
  3. 检查照片保存目录的写入权限
  4. 重启相机设备

**问题2：照片质量差**
- **现象：** 拍摄的照片模糊或光线不足
- **可能原因：** 相机焦距问题、光线不足、镜头脏污
- **解决方案：**
  1. 调整相机焦距设置
  2. 检查拍照区域光线是否充足
  3. 清洁相机镜头
  4. 调整产品摆放位置

**问题3：水印添加失败**
- **现象：** 照片拍摄成功但水印状态显示"处理失败"
- **可能原因：** 图片文件损坏、磁盘空间不足、水印服务异常
- **解决方案：**
  1. 检查原始照片文件是否正常
  2. 确保有足够的磁盘空间
  3. 重启水印处理服务
  4. 检查系统日志获取详细错误信息

### 5.4 打印问题

**问题1：小盒标签打印失败**
- **现象：** 系统提示打印失败，标签无法打印
- **可能原因：** BarTender服务异常、打印机故障、模板文件问题
- **解决方案：**
  1. 检查BarTender服务是否运行正常
  2. 检查打印机连接和状态
  3. 确认标签模板文件是否存在
  4. 重启BarTender服务

**问题2：大箱标签打印失败**
- **现象：** 大箱标签无法打印，系统提示网络错误
- **可能原因：** 网络连接问题、BarTender服务异常、模板问题
- **解决方案：**
  1. 检查网络连接是否正常
  2. 确认BarTender服务运行状态
  3. 检查大箱标签模板文件
  4. 重启打印服务

**问题3：打印内容错误**
- **现象：** 标签打印成功但内容信息错误
- **可能原因：** 数据传输错误、模板变量设置错误、缓存问题
- **解决方案：**
  1. 检查传输的数据是否正确
  2. 确认模板变量名称设置
  3. 清理BarTender缓存
  4. 重新打印标签

### 5.5 数据异常问题

**问题1：生产数据丢失**
- **现象：** 部分生产记录在系统中找不到
- **可能原因：** 数据库连接异常、事务回滚、系统异常关闭
- **解决方案：**
  1. 检查数据库连接状态
  2. 查看系统日志确认操作记录
  3. 检查数据库备份文件
  4. 联系技术人员恢复数据

**问题2：队列数据异常**
- **现象：** 队列显示的数据与实际不符
- **可能原因：** 内存数据与数据库不同步、系统缓存问题
- **解决方案：**
  1. 重启软件程序刷新数据
  2. 检查数据库中的实际数据
  3. 清理系统缓存
  4. 手动同步队列数据

**问题3：统计数据错误**
- **现象：** 生产统计报表数据明显错误
- **可能原因：** 统计算法问题、数据源异常、时间范围设置错误
- **解决方案：**
  1. 检查查询条件设置是否正确
  2. 确认数据源的完整性
  3. 重新计算统计数据
  4. 对比原始数据验证结果

---

## 第六章 注意事项与安全须知

### 6.1 操作注意事项

**日常操作规范：**
1. **登录管理：** 每个操作员必须使用自己的账号登录，不得共用账号
2. **模式选择：** 根据实际生产需要选择合适的生产模式，不得随意切换
3. **设备检查：** 每班开始前检查所有设备连接状态，确保正常工作
4. **异常处理：** 遇到异常情况及时停机处理，不得强行继续生产
5. **数据确认：** 重要操作前确认数据正确性，避免误操作

**质量控制要点：**
1. **条码管理：** 确保条码清晰完整，避免损坏或污染
2. **产品摆放：** 拍照时确保产品摆放位置正确，便于拍摄
3. **异常产品：** 及时取走不合格产品，避免混入合格品中
4. **标签检查：** 打印后检查标签内容是否正确完整
5. **重量确认：** 装箱时确认重量读数准确，避免异物影响

**效率提升建议：**
1. **批量操作：** 合理安排生产批次，提高生产效率
2. **设备维护：** 定期清洁设备，保持最佳工作状态
3. **流程优化：** 熟悉操作流程，减少不必要的等待时间
4. **问题预防：** 主动发现和解决潜在问题，避免停机
5. **技能提升：** 不断学习和掌握系统新功能

### 6.2 数据安全

**数据保护措施：**
1. **定期备份：** 系统自动备份重要数据，建议定期检查备份完整性
2. **权限控制：** 严格按照权限使用系统功能，不得越权操作
3. **密码安全：** 定期更换登录密码，不得泄露给他人
4. **数据导出：** 谨慎使用数据导出功能，确保数据不外泄
5. **系统日志：** 重要操作都有日志记录，便于问题追溯

**数据完整性保障：**
1. **操作确认：** 重要操作前仔细确认，避免误删除数据
2. **异常报告：** 发现数据异常及时报告，不得私自修改
3. **版本控制：** 系统升级前做好数据备份，确保数据安全
4. **访问控制：** 限制数据库直接访问，通过系统界面操作
5. **审计跟踪：** 所有数据变更都有完整的审计记录

### 6.3 设备维护

**日常维护要求：**
1. **清洁保养：** 定期清洁设备表面和镜头，保持设备清洁
2. **连接检查：** 定期检查各种连接线缆，确保连接牢固
3. **环境控制：** 保持工作环境清洁干燥，避免灰尘和潮湿
4. **温度控制：** 确保设备工作环境温度适宜，避免过热
5. **防护措施：** 做好设备防护，避免碰撞和损坏

**定期维护计划：**
1. **每日检查：** 检查设备运行状态和连接情况
2. **每周清洁：** 清洁设备表面和工作区域
3. **每月校准：** 校准电子秤和相机等精密设备
4. **季度保养：** 全面检查和保养所有设备
5. **年度检修：** 专业技术人员进行全面检修

**故障预防：**
1. **预警监控：** 关注设备状态指示，及时发现异常
2. **备件准备：** 准备常用备件，确保快速更换
3. **技术支持：** 建立技术支持联系方式，及时获得帮助
4. **培训学习：** 定期参加设备操作和维护培训
5. **记录管理：** 详细记录设备维护和故障处理情况

---

**文档结束**

*本操作指导书为海尔插座生产装箱扫码拍照系统的完整操作手册，涵盖了系统的所有主要功能和操作流程。如有疑问或需要技术支持，请联系系统管理员或技术支持人员。*