using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;

namespace WatermarkTest
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("水印功能测试程序");
            
            // 创建一个测试图片
            string testImagePath = "test_image.jpg";
            CreateTestImage(testImagePath);
            
            // 添加水印
            string watermarkText = "HE240001234567";
            AddWatermark(testImagePath, watermarkText);
            
            Console.WriteLine($"测试完成，请查看文件: {testImagePath}");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
        
        static void CreateTestImage(string path)
        {
            using (var bitmap = new Bitmap(800, 600))
            {
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    // 填充白色背景
                    graphics.Clear(Color.White);
                    
                    // 绘制一些测试内容
                    using (var brush = new SolidBrush(Color.Blue))
                    using (var font = new Font("Arial", 24))
                    {
                        graphics.DrawString("测试图片", font, brush, 50, 50);
                        graphics.DrawString("Test Image", font, brush, 50, 100);
                    }
                    
                    // 绘制一个矩形
                    using (var pen = new Pen(Color.Red, 3))
                    {
                        graphics.DrawRectangle(pen, 100, 200, 300, 200);
                    }
                }
                
                bitmap.Save(path, ImageFormat.Jpeg);
            }
            
            Console.WriteLine($"创建测试图片: {path}");
        }
        
        static void AddWatermark(string imagePath, string watermarkText)
        {
            try
            {
                using (var originalImage = Image.FromFile(imagePath))
                {
                    using (var watermarkedImage = new Bitmap(originalImage.Width, originalImage.Height))
                    {
                        using (var graphics = Graphics.FromImage(watermarkedImage))
                        {
                            // 设置高质量渲染
                            graphics.SmoothingMode = SmoothingMode.AntiAlias;
                            graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.AntiAlias;

                            // 绘制原始图片
                            graphics.DrawImage(originalImage, 0, 0);

                            // 计算字体大小，使文本宽度占图片宽度的80%
                            var fontSize = CalculateFontSize(graphics, watermarkText, originalImage.Width * 0.8f);
                            
                            using (var font = new Font("Arial", fontSize, FontStyle.Bold))
                            {
                                // 创建70%透明的黄色画刷
                                using (var brush = new SolidBrush(Color.FromArgb(179, Color.Yellow))) // 179 = 255 * 0.7
                                {
                                    // 测量文本大小
                                    var textSize = graphics.MeasureString(watermarkText, font);
                                    
                                    // 计算文本位置（居中）
                                    var x = (originalImage.Width - textSize.Width) / 2;
                                    var y = (originalImage.Height - textSize.Height) / 2;

                                    // 绘制水印文本
                                    graphics.DrawString(watermarkText, font, brush, x, y);
                                    
                                    Console.WriteLine($"添加水印: {watermarkText}");
                                    Console.WriteLine($"字体大小: {fontSize}");
                                    Console.WriteLine($"文本大小: {textSize.Width} x {textSize.Height}");
                                    Console.WriteLine($"位置: ({x}, {y})");
                                }
                            }
                        }

                        // 保存带水印的图片，覆盖原文件
                        var tempPath = imagePath + ".tmp";
                        watermarkedImage.Save(tempPath, ImageFormat.Jpeg);
                        
                        // 替换原文件
                        File.Delete(imagePath);
                        File.Move(tempPath, imagePath);
                    }
                }

                Console.WriteLine($"成功为图片添加水印: {imagePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"为图片添加水印失败: {ex.Message}");
            }
        }
        
        static float CalculateFontSize(Graphics graphics, string text, float targetWidth)
        {
            float fontSize = 12f;
            float maxFontSize = 200f;
            float minFontSize = 8f;

            while (fontSize <= maxFontSize)
            {
                using (var font = new Font("Arial", fontSize, FontStyle.Bold))
                {
                    var textSize = graphics.MeasureString(text, font);
                    if (textSize.Width >= targetWidth)
                    {
                        return Math.Max(fontSize - 1, minFontSize);
                    }
                }
                fontSize += 1f;
            }

            return maxFontSize;
        }
    }
}
