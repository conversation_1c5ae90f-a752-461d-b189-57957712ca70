using OutletProductionPacking.Data.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OutletProductionPacking.Data.Repositories
{
    public interface IProductionOrderBarcodeRepository
    {
        Task<List<ProductionOrderBarcode>> GetByOrderIdAsync(int orderId);
        Task<ProductionOrderBarcode> GetByBarcodeAsync(string barcode);
        Task<bool> ExistsAsync(string barcode);
        Task<bool> HasProducedBarcodesAsync(int orderId);
        Task DeleteByOrderIdAsync(int orderId);
        Task<List<ProductionOrderBarcode>> AddRangeAsync(List<ProductionOrderBarcode> barcodes);
        Task UpdateAsync(ProductionOrderBarcode barcode);

        /// <summary>
        /// 根据盒号获取条码列表
        /// </summary>
        /// <param name="boxNumber">盒号</param>
        /// <returns>条码列表</returns>
        Task<List<string>> GetBarcodesByBoxNumberAsync(string boxNumber);
        Task<string?> GetBoxNumberByBarCodeAsync(string barCode);
    }
}
