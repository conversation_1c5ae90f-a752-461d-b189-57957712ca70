<UserControl x:Class="OutletProductionPacking.WPF.Views.UserControls.BarcodeTraceView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             Background="{StaticResource BackgroundBrush}"
             d:DesignHeight="800" d:DesignWidth="1200">
    
    <UserControl.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        
        <!-- 反向布尔转换器 -->
        <Style x:Key="InverseBoolConverter" TargetType="Button">
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsSearching}" Value="True">
                    <Setter Property="IsEnabled" Value="False"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
        
        <!-- 字符串到可见性转换器 -->
        <Style x:Key="StringToVisibilityStyle" TargetType="TextBlock">
            <Style.Triggers>
                <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource Self}}" Value="">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource Self}}" Value="{x:Null}">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 搜索区域 -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="15" CornerRadius="5" Margin="0,0,0,15">
            <StackPanel>
                <TextBlock Text="条码追溯查询" FontSize="18" FontWeight="Bold" Foreground="White" Margin="0,0,0,15"/>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="条码:" VerticalAlignment="Center" Foreground="White" Margin="0,0,10,0" FontSize="14"/>
                    <TextBox x:Name="BarcodeTextBox" Text="{Binding SearchBarcode, UpdateSourceTrigger=PropertyChanged}" 
                             Width="300" Height="32" FontSize="14" VerticalContentAlignment="Center"
                             BorderBrush="{StaticResource BorderBrush}" BorderThickness="1">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>
                    <Button Content="查询" Command="{Binding SearchCommand}" Margin="15,0,0,0" 
                            Width="80" Height="32" Style="{StaticResource InverseBoolConverter}"
                            Background="{StaticResource SecondaryBrush}" Foreground="White" FontWeight="Bold"/>
                    <Button Content="清空" Command="{Binding ClearCommand}" Margin="10,0,0,0" 
                            Width="80" Height="32" Background="{StaticResource BackgroundLightBrush}" 
                            BorderBrush="{StaticResource BorderBrush}" BorderThickness="1"/>
                </StackPanel>
                
                <!-- 错误信息 -->
                <TextBlock Text="{Binding ErrorMessage}" Foreground="Yellow" Margin="0,10,0,0" FontWeight="Bold"
                           Style="{StaticResource StringToVisibilityStyle}"/>
                
                <!-- 加载提示 -->
                <TextBlock Text="正在查询..." Margin="0,10,0,0" Foreground="White" FontStyle="Italic"
                           Visibility="{Binding IsSearching, Converter={StaticResource BoolToVisibilityConverter}}"/>
            </StackPanel>
        </Border>
        
        <!-- 结果显示区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled"
                      Visibility="{Binding HasResult, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel>
                <!-- 基本信息 -->
                <GroupBox Header="📋 基本信息" Margin="0,0,0,15" FontSize="14" FontWeight="Bold">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="条码:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding TraceInfo.Barcode}" Margin="5" 
                                   FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="订单号:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding TraceInfo.OrderNumber}" Margin="5" VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="产品型号:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding TraceInfo.ProductCode}" Margin="5" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="2" Text="产品名称:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="3" Text="{Binding TraceInfo.ProductName}" Margin="5" VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="规格:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding TraceInfo.ProductSpec}" Margin="5" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="2" Grid.Column="2" Text="生产日期:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="2" Grid.Column="3" Text="{Binding TraceInfo.ProductionDate, StringFormat=yyyy-MM-dd}" Margin="5" VerticalAlignment="Center"/>
                    </Grid>
                </GroupBox>
                
                <!-- 质检信息 -->
                <GroupBox Header="🔍 质检信息" Margin="0,0,0,15" FontSize="14" FontWeight="Bold"
                          Visibility="{Binding HasQualityInfo, Converter={StaticResource BoolToVisibilityConverter}}">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="质检时间:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding TraceInfo.QualityInfo.InspectionTime, StringFormat=yyyy-MM-dd HH:mm:ss}" 
                                   Margin="5" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="质检结果:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding QualityResultText}" 
                                   Margin="5" FontWeight="Bold" Foreground="{Binding QualityResultColor}" VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="生产模式:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding TraceInfo.QualityInfo.ProductionMode}" Margin="5" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="2" Text="操作员:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="3" Text="{Binding TraceInfo.QualityInfo.OperatorName}" Margin="5" VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="DI状态:" Margin="5" FontWeight="Bold" VerticalAlignment="Top"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" Text="{Binding TraceInfo.QualityInfo.DiStatus}" 
                                   Margin="5" TextWrapping="Wrap" FontFamily="Consolas" VerticalAlignment="Center"/>
                    </Grid>
                </GroupBox>
                
                <!-- 拍照信息 -->
                <GroupBox Header="📷 拍照信息" Margin="0,0,0,15" FontSize="14" FontWeight="Bold"
                          Visibility="{Binding HasPhotoInfo, Converter={StaticResource BoolToVisibilityConverter}}">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="拍照时间:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding TraceInfo.PhotoInfo.PhotoTime, StringFormat=yyyy-MM-dd HH:mm:ss}" 
                                   Margin="5" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="生产模式:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding TraceInfo.PhotoInfo.ProductionMode}" Margin="5" VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="水印状态:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding TraceInfo.PhotoInfo.WatermarkStatus}" Margin="5" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="2" Text="操作员:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="3" Text="{Binding TraceInfo.PhotoInfo.OperatorName}" Margin="5" VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="照片路径:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding TraceInfo.PhotoInfo.PhotoPath}" 
                                   Margin="5" TextWrapping="Wrap" FontFamily="Consolas" VerticalAlignment="Center"/>
                        <Button Grid.Row="2" Grid.Column="3" Content="查看照片" Command="{Binding ViewPhotoCommand}" 
                                Width="100" Height="30" HorizontalAlignment="Left" Margin="5"
                                Background="{StaticResource PrimaryBrush}" Foreground="White"/>
                    </Grid>
                </GroupBox>
                
                <!-- 包装信息 -->
                <GroupBox Header="📦 包装信息" Margin="0,0,0,15" FontSize="14" FontWeight="Bold"
                          Visibility="{Binding HasPackagingInfo, Converter={StaticResource BoolToVisibilityConverter}}">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="装盒时间:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding TraceInfo.PackageInfo.BoxTime, StringFormat=yyyy-MM-dd HH:mm:ss}" 
                                   Margin="5" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="盒号:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding TraceInfo.PackageInfo.BoxNumber}" 
                                   Margin="5" FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="装箱时间:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding TraceInfo.PackageInfo.CartonTime, StringFormat=yyyy-MM-dd HH:mm:ss}" 
                                   Margin="5" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="2" Text="箱号:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="1" Grid.Column="3" Text="{Binding TraceInfo.PackageInfo.CartonNumber}" 
                                   Margin="5" FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="箱重:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding TraceInfo.PackageInfo.CartonWeight, StringFormat={}{0:F1}kg}" 
                                   Margin="5" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="2" Grid.Column="2" Text="总数量:" Margin="5" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Grid.Row="2" Grid.Column="3" Text="{Binding TraceInfo.PackageInfo.TotalQuantity, StringFormat={}{0}个}" 
                                   Margin="5" VerticalAlignment="Center"/>
                    </Grid>
                </GroupBox>
                
                <!-- 生产历程 -->
                <GroupBox Header="📈 生产历程" FontSize="14" FontWeight="Bold">
                    <DataGrid ItemsSource="{Binding TraceInfo.ProductionSteps}" AutoGenerateColumns="False" 
                              IsReadOnly="True" GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                              RowHeight="30" FontSize="12" Margin="5"
                              Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="1">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="时间" Binding="{Binding StepTime, StringFormat=yyyy-MM-dd HH:mm:ss}" Width="150"/>
                            <DataGridTextColumn Header="工序" Binding="{Binding StepName}" Width="100"/>
                            <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="100"/>
                            <DataGridTextColumn Header="生产模式" Binding="{Binding ProductionMode}" Width="100"/>
                            <DataGridTextColumn Header="详情" Binding="{Binding Details}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
