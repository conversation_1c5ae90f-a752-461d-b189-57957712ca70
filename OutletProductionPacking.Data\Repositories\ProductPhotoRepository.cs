using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OutletProductionPacking.Data.Repositories
{
    public class ProductPhotoRepository : IProductPhotoRepository
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public ProductPhotoRepository(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<ProductPhoto> GetByIdAsync(int id)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductPhotos.FindAsync(id);
        }

        public async Task<List<ProductPhoto>> GetByBarcodeAsync(string barcode)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductPhotos
                .Where(p => p.Barcode == barcode)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();
        }

        public async Task<bool> ExistsByBarcodeAsync(string barcode)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductPhotos
                .AnyAsync(p => p.Barcode == barcode);
        }

        public async Task<ProductPhoto> AddAsync(ProductPhoto photo)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            await context.ProductPhotos.AddAsync(photo);
            await context.SaveChangesAsync();
            return photo;
        }

        public async Task<List<ProductPhoto>> GetByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductPhotos
                .Where(p => p.OrderId == orderId)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<ProductPhoto>> GetRecentPhotosAsync(int count = 100)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductPhotos
                .OrderByDescending(p => p.CreatedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task<List<ProductPhotoWithOrderInfo>> GetRecentPhotosWithOrderInfoAsync(int count = 100)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var query = from pp in context.ProductPhotos
                        join po in context.ProductionOrders on pp.OrderId equals po.Id
                        join p in context.Products on po.ProductCode equals p.Code
                        orderby pp.CreatedAt descending
                        select new ProductPhotoWithOrderInfo
                        {
                            Id = pp.Id,
                            Barcode = pp.Barcode,
                            OrderId = pp.OrderId,
                            PhotoPath = pp.PhotoPath,
                            CreatedAt = pp.CreatedAt,
                            OperatorId = pp.OperatorId,
                            OrderNumber = po.OrderNumber,
                            ProductSpecification = p.Specification ?? string.Empty,
                            ProductCategory = p.Category
                        };

            return await query.Take(count).ToListAsync();
        }

        public async Task<List<ProductPhotoWithOrderInfo>> GetRecentPhotosByOrderIdAsync(int orderId, int count = 100)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var query = from pp in context.ProductPhotos
                        join po in context.ProductionOrders on pp.OrderId equals po.Id
                        join p in context.Products on po.ProductCode equals p.Code
                        where pp.OrderId == orderId
                        orderby pp.CreatedAt descending
                        select new ProductPhotoWithOrderInfo
                        {
                            Id = pp.Id,
                            Barcode = pp.Barcode,
                            OrderId = pp.OrderId,
                            PhotoPath = pp.PhotoPath,
                            CreatedAt = pp.CreatedAt,
                            OperatorId = pp.OperatorId,
                            OrderNumber = po.OrderNumber,
                            ProductSpecification = p.Specification ?? string.Empty,
                            ProductCategory = p.Category
                        };

            return await query.Take(count).ToListAsync();
        }
    }
}
