using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OutletProductionPacking.Data.Repositories
{
    public class QualityInspectionRepository : IQualityInspectionRepository
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public QualityInspectionRepository(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<QualityInspection> GetByIdAsync(int id)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.QualityInspections.FindAsync(id);
        }

        public async Task<List<QualityInspection>> GetByBarcodeAsync(string barcode)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.QualityInspections
                .Where(q => q.Barcode == barcode)
                .OrderByDescending(q => q.CreatedAt)
                .ToListAsync();
        }

        public async Task<bool> ExistsByBarcodeAsync(string barcode)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.QualityInspections
                .AnyAsync(q => q.Barcode == barcode);
        }

        public async Task<QualityInspection> AddAsync(QualityInspection inspection)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            await context.QualityInspections.AddAsync(inspection);
            await context.SaveChangesAsync();
            return inspection;
        }

        public async Task<QualityInspection> UpdateAsync(QualityInspection inspection)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            context.QualityInspections.Update(inspection);
            await context.SaveChangesAsync();
            return inspection;
        }

        public async Task<List<QualityInspection>> GetByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.QualityInspections
                .Where(q => q.OrderId == orderId)
                .OrderByDescending(q => q.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<QualityInspection>> GetRecentInspectionsAsync(int count = 100)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.QualityInspections
                .OrderByDescending(q => q.CreatedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task<List<QualityInspectionWithOrderInfo>> GetRecentInspectionsWithOrderInfoAsync(int count = 100)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var query = from qi in context.QualityInspections
                        join po in context.ProductionOrders on qi.OrderId equals po.Id
                        join p in context.Products on qi.ProductId equals p.Id
                        orderby qi.CreatedAt descending
                        select new QualityInspectionWithOrderInfo
                        {
                            Id = qi.Id,
                            Barcode = qi.Barcode,
                            OrderId = qi.OrderId,
                            ProductId = qi.ProductId,
                            ProductCategory = qi.ProductCategory,
                            Result = qi.Result,
                            DiStatusJson = qi.DiStatusJson,
                            OperatorId = qi.OperatorId,
                            Remarks = qi.Remarks,
                            CreatedAt = qi.CreatedAt,
                            OrderNumber = po.OrderNumber,
                            ProductSpecification = p.Specification ?? string.Empty
                        };

            return await query.Take(count).ToListAsync();
        }

        public async Task<List<QualityInspectionWithOrderInfo>> GetRecentInspectionsByOrderIdAsync(int orderId, int count = 100)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var query = from qi in context.QualityInspections
                        join po in context.ProductionOrders on qi.OrderId equals po.Id
                        join p in context.Products on qi.ProductId equals p.Id
                        where qi.OrderId == orderId
                        orderby qi.CreatedAt descending
                        select new QualityInspectionWithOrderInfo
                        {
                            Id = qi.Id,
                            Barcode = qi.Barcode,
                            OrderId = qi.OrderId,
                            ProductId = qi.ProductId,
                            ProductCategory = qi.ProductCategory,
                            Result = qi.Result,
                            DiStatusJson = qi.DiStatusJson,
                            OperatorId = qi.OperatorId,
                            Remarks = qi.Remarks,
                            CreatedAt = qi.CreatedAt,
                            OrderNumber = po.OrderNumber,
                            ProductSpecification = p.Specification ?? string.Empty
                        };

            return await query.Take(count).ToListAsync();
        }
    }
}
