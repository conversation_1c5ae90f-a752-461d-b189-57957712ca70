﻿using NModbus;
using System.Net.Sockets;

namespace OutletProductionPacking.Core.Services
{
    public class ModbusService : IModbusService, IDisposable
    {
        private TcpClient? _client;
        private IModbusMaster? _master;
        private const byte SlaveId = 0; // 西门子S7-200 Smart默认站号为1
        private const int DefaultTimeout = 5000; // 默认超时时间5秒

        public bool IsConnected => _client?.Connected ?? false;

        public async Task<bool> ConnectAsync(string ipAddress, int port = 502)
        {
            try
            {
                _client = new TcpClient();

                // 设置连接超时
                var connectTask = _client.ConnectAsync(ipAddress, port);
                var timeoutTask = Task.Delay(DefaultTimeout);
                var completedTask = await Task.WhenAny(connectTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    throw new TimeoutException($"连接超时：{ipAddress}:{port}");
                }

                await connectTask; // 确保连接任务完成

                var factory = new ModbusFactory();
                _master = factory.CreateMaster(_client);

                // 配置Modbus主站超时设置
                _master.Transport.ReadTimeout = DefaultTimeout;
                _master.Transport.WriteTimeout = DefaultTimeout;
                _master.Transport.Retries = 3; // 重试次数
                _master.Transport.WaitToRetryMilliseconds = 500; // 重试等待时间

                return true;
            }
            catch (Exception)
            {
                Disconnect();
                return false;
            }
        }

        public void Disconnect()
        {
            _master?.Dispose();
            _master = null;

            if (_client?.Connected ?? false)
            {
                _client.Close();
            }
            _client?.Dispose();
            _client = null;
        }

        public async Task<bool[]> ReadInputsAsync(ushort startAddress, ushort count)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            // EBYTE模块的DI对应功能码02
            var result = await _master.ReadInputsAsync(SlaveId, startAddress, count);
            return result;
        }

        public async Task<bool> WriteCoilAsync(ushort coilAddress, bool value)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            // EBYTE模块的DO对应功能码05
            await _master.WriteSingleCoilAsync(SlaveId, coilAddress, value);
            return true;
        }
        public async Task<bool> WriteRegisterAsync(ushort registerAddress, ushort value)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            // EBYTE模块的DO对应功能码05
            await _master.WriteSingleRegisterAsync(SlaveId, registerAddress, value);
            return true;
        }

        public async Task WriteCoilsAsync(ushort startAddress, bool[] values)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            // EBYTE模块的DO对应功能码0F
            await _master.WriteMultipleCoilsAsync(SlaveId, startAddress, values);
        }

        #region 西门子S7-200 Smart V区读写方法

        /// <summary>
        /// 读取保持寄存器（功能码03）
        /// </summary>
        public async Task<ushort[]> ReadHoldingRegistersAsync(ushort startAddress, ushort count)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            var result = await _master.ReadHoldingRegistersAsync(SlaveId, startAddress, count);
            return result;
        }

        /// <summary>
        /// 写入多个保持寄存器（功能码16）
        /// </summary>
        public async Task WriteHoldingRegistersAsync(ushort startAddress, ushort[] values)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            await _master.WriteMultipleRegistersAsync(SlaveId, startAddress, values);
        }

        /// <summary>
        /// 读取V区字节数据（如V0.0的字节值）
        /// </summary>
        public async Task<byte> ReadVByteAsync(ushort vAddress)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            // V地址转换为Modbus寄存器地址：V0.0 -> 寄存器0, V1.0 -> 寄存器2
            ushort registerAddress = (ushort)(vAddress / 2);
            var registers = await _master.ReadHoldingRegistersAsync(SlaveId, registerAddress, 1);

            // 根据V地址的奇偶性决定取高字节还是低字节
            if (vAddress % 2 == 0)
                return (byte)(registers[0] & 0xFF); // 低字节
            else
                return (byte)((registers[0] >> 8) & 0xFF); // 高字节
        }

        /// <summary>
        /// 写入V区字节数据
        /// </summary>
        public async Task WriteVByteAsync(ushort vAddress, byte value)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            ushort registerAddress = (ushort)(vAddress / 2);
            var registers = await _master.ReadHoldingRegistersAsync(SlaveId, registerAddress, 1);

            ushort newValue;
            if (vAddress % 2 == 0)
                newValue = (ushort)((registers[0] & 0xFF00) | value); // 保持高字节，更新低字节
            else
                newValue = (ushort)((registers[0] & 0x00FF) | (value << 8)); // 保持低字节，更新高字节

            await _master.WriteSingleRegisterAsync(SlaveId, registerAddress, newValue);
        }

        /// <summary>
        /// 读取V区字数据（如V0.0的字值，占用V0.0和V0.1）
        /// </summary>
        public async Task<ushort> ReadVWordAsync(ushort vAddress)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            ushort registerAddress = (ushort)(vAddress / 2);
            var registers = await _master.ReadHoldingRegistersAsync(SlaveId, registerAddress, 1);
            return registers[0];
        }

        /// <summary>
        /// 写入V区字数据
        /// </summary>
        public async Task WriteVWordAsync(ushort vAddress, ushort value)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            ushort registerAddress = (ushort)(vAddress / 2);
            await _master.WriteSingleRegisterAsync(SlaveId, registerAddress, value);
        }

        /// <summary>
        /// 读取V区位数据（如V0.0的第0位）
        /// </summary>
        public async Task<bool> ReadVBitAsync(ushort vAddress, byte bitIndex)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");
            if (bitIndex > 7) throw new ArgumentException("位索引必须在0-7之间");

            byte byteValue = await ReadVByteAsync(vAddress);
            return (byteValue & (1 << bitIndex)) != 0;
        }

        /// <summary>
        /// 写入V区位数据
        /// </summary>
        public async Task WriteVBitAsync(ushort vAddress, byte bitIndex, bool value)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");
            if (bitIndex > 7) throw new ArgumentException("位索引必须在0-7之间");

            byte byteValue = await ReadVByteAsync(vAddress);

            if (value)
                byteValue |= (byte)(1 << bitIndex); // 设置位
            else
                byteValue &= (byte)~(1 << bitIndex); // 清除位

            await WriteVByteAsync(vAddress, byteValue);
        }

        #endregion

        public void Dispose()
        {
            Disconnect();
        }
    }
}
