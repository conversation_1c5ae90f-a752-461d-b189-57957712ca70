# 停线问题排查和修复说明

## 📋 问题描述

用户反馈：在正常生产模式下，扫到已拍照过的条码或质检不合格的条码时，应该停线，但现在不停线了。

## 🔍 问题排查

### 1. 代码逻辑检查

经过检查，发现停线逻辑是正确的：

#### ValidatePhotoBarcodeAsync方法中的停线调用
```csharp
// 正常生产模式下的验证逻辑
if (!IsPackageRepairMode)
{
    // 检查漏检产品
    if (latestQualityResult == null)
    {
        PhotoStatus = "❌ 漏检产品，请取走产品";
        HasPhotoError = true;
        AddStatusMessage($"⚠️ 条码 {barcode} 漏检，线体已自动停止");
        await StopLineForQualityAsync(); // ✅ 正确调用停线
        return false;
    }
    
    // 检查质检不合格产品
    else if (latestQualityResult == false)
    {
        PhotoStatus = "❌ 产品不合格，请取走产品";
        HasPhotoError = true;
        AddStatusMessage($"⚠️ 条码 {barcode} 质量检测不合格，线体已自动停止");
        await StopLineForQualityAsync(); // ✅ 正确调用停线
        return false;
    }
    
    // 检查已拍照产品
    bool hasPhotoRecord = await _productionOrderService.HasProductPhotoAsync(barcode);
    if (hasPhotoRecord)
    {
        PhotoStatus = "❌ 条码已拍照，请取走产品";
        HasPhotoError = true;
        AddStatusMessage($"⚠️ 条码 {barcode} 已有拍照记录，线体已自动停止");
        await StopLineForQualityAsync(); // ✅ 正确调用停线
        return false;
    }
}
```

### 2. StopLineForQualityAsync方法检查

```csharp
private async Task StopLineForQualityAsync()
{
    try
    {
        if (_modbusService.IsConnected)
        {
            // 停止线体运行：V0.256
            await _modbusService.WriteVWordAsync(0, 256);
            AddStatusMessage("⚠️ 检测到不合格产品，线体已自动停止");
        }
        else
        {
            AddStatusMessage("⚠️ 检测到不合格产品，线体应停止（IO模块未连接，无法控制实际线体）");
        }

        // 启动DI8监控，等待用户取走不合格产品
        StartDI8Monitoring();
    }
    catch (Exception ex)
    {
        _logService.Error(ex, "停止线体运行失败");
        AddStatusMessage($"❌ 停止线体运行失败: {ex.Message}");
    }
}
```

### 3. 发现的问题

#### 问题1：V3.0地址错误（已修复）
在`HandleDuplicateQualifiedBarcodeAsync`方法中发现地址错误：

**修复前（错误）**：
```csharp
await _modbusService.WriteVWordAsync(1, 1);  // 错误：写入V0.1而不是V3.0
await _modbusService.WriteVWordAsync(1, 0);  // 错误：复位V0.1而不是V3.0
```

**修复后（正确）**：
```csharp
await _modbusService.WriteVWordAsync(3, 1);  // 正确：写入V3.0
await _modbusService.WriteVWordAsync(3, 0);  // 正确：复位V3.0
```

#### 问题2：可能的IO模块连接问题
如果`_modbusService.IsConnected`为false，停线命令不会发送到硬件，只会显示提示信息。

## 🔧 可能的原因分析

### 1. IO模块未连接
- **现象**：软件显示停线提示，但实际线体没有停止
- **原因**：`_modbusService.IsConnected`为false
- **解决方案**：检查IO模块连接状态

### 2. V0.256地址不正确
- **现象**：IO模块已连接，但线体没有停止
- **原因**：V0.256可能不是正确的停线地址
- **解决方案**：确认正确的停线地址

### 3. 硬件配置问题
- **现象**：地址正确但线体不响应
- **原因**：PLC程序中V0.256没有配置为停线信号
- **解决方案**：检查PLC程序配置

## 📝 修复内容

### 1. 修复V3.0地址错误
- 文件：`WorkspaceViewModel.cs`
- 方法：`HandleDuplicateQualifiedBarcodeAsync`
- 修改：将V0.1改为V3.0，将延迟从2秒改为1秒

### 2. 添加调试信息
建议在停线时添加更多调试信息：

```csharp
private async Task StopLineForQualityAsync()
{
    try
    {
        AddStatusMessage($"🔧 调试：IO模块连接状态 = {_modbusService.IsConnected}");
        
        if (_modbusService.IsConnected)
        {
            AddStatusMessage("🔧 调试：正在向V0.256写入256...");
            await _modbusService.WriteVWordAsync(0, 256);
            AddStatusMessage("⚠️ 检测到不合格产品，线体已自动停止");
            AddStatusMessage("🔧 调试：V0.256写入完成");
        }
        else
        {
            AddStatusMessage("⚠️ 检测到不合格产品，线体应停止（IO模块未连接，无法控制实际线体）");
        }

        StartDI8Monitoring();
    }
    catch (Exception ex)
    {
        _logService.Error(ex, "停止线体运行失败");
        AddStatusMessage($"❌ 停止线体运行失败: {ex.Message}");
    }
}
```

## 🔍 排查步骤

### 1. 检查IO模块连接状态
- 查看界面上的IO模块连接指示灯
- 检查日志中的连接状态信息

### 2. 检查停线地址配置
- 确认V0.256是否为正确的停线地址
- 检查PLC程序中的地址映射

### 3. 测试停线功能
- 使用"测试停线"按钮验证停线功能
- 观察线体是否实际停止

### 4. 检查恢复线体功能
- 测试手动恢复线体功能
- 验证DI8自动恢复功能

## ⚠️ 注意事项

### 1. 地址映射
- V0.0 = 寄存器地址0
- V0.1 = 寄存器地址0的高字节
- V3.0 = 寄存器地址3

### 2. 停线逻辑
- 停线：`WriteVWordAsync(0, 256)`
- 恢复：`WriteVWordAsync(0, 0)`

### 3. 模式区分
- 正常生产模式：严格验证，检测到问题立即停线
- 包装返修模式：宽松验证，允许重复和不合格产品

## 🎯 验证方法

### 1. 正常生产模式测试
1. 扫描已拍照过的条码
2. 观察是否显示"条码已拍照，请取走产品"
3. 观察是否显示"线体已自动停止"
4. 检查实际线体是否停止

### 2. 质检不合格产品测试
1. 扫描质检不合格的条码
2. 观察是否显示"产品不合格，请取走产品"
3. 观察是否显示"线体已自动停止"
4. 检查实际线体是否停止

### 3. IO模块连接测试
1. 断开IO模块连接
2. 扫描问题条码
3. 观察是否显示"IO模块未连接，无法控制实际线体"

## 📊 预期结果

- ✅ 正常生产模式下检测到问题条码时立即停线
- ✅ 显示明确的错误提示信息
- ✅ 启动DI8监控等待用户处理
- ✅ 用户取走产品后自动恢复线体运行
- ✅ V3.0信号正确发送（重复扫描已合格条码时）

这次修复解决了V3.0地址错误的问题，如果停线功能仍然不正常，需要进一步检查IO模块连接和硬件配置。
