using System;

namespace OutletProductionPacking.Data.Models
{
    public class QualityInspection
    {
        public int Id { get; set; }
        public string Barcode { get; set; }
        public int OrderId { get; set; }
        public int ProductId { get; set; }
        public string ProductCategory { get; set; }
        public bool Result { get; set; }
        public string DiStatus<PERSON>son { get; set; }
        public int OperatorId { get; set; }
        public string Remarks { get; set; }
        public DateTime CreatedAt { get; set; }
        /// <summary>
        /// 生产模式：0=正常生产，1=包装返修，2=无检测生产
        /// </summary>
        public short ProductionMode { get; set; } = 0;
    }
}
