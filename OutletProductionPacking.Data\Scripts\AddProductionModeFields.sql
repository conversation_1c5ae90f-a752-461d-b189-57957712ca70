-- 为生产数据表添加生产模式字段
-- 执行日期: 2024-12-17

-- 1. 为QualityInspection表添加ProductionMode字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'QualityInspections' 
     AND COLUMN_NAME = 'ProductionMode') = 0,
    'ALTER TABLE QualityInspections ADD COLUMN ProductionMode SMALLINT DEFAULT 0 COMMENT ''生产模式：0=正常生产，1=包装返修，2=无检测生产'';',
    'SELECT ''字段QualityInspection.ProductionMode已存在，跳过添加'';'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 为ProductPhotos表添加ProductionMode字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'ProductPhotos' 
     AND COLUMN_NAME = 'ProductionMode') = 0,
    'ALTER TABLE ProductPhotos ADD COLUMN ProductionMode SMALLINT DEFAULT 0 COMMENT ''生产模式：0=正常生产，1=包装返修，2=无检测生产'';',
    'SELECT ''字段ProductPhotos.ProductionMode已存在，跳过添加'';'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 为BoxPackages表添加ProductionMode字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'BoxPackages' 
     AND COLUMN_NAME = 'ProductionMode') = 0,
    'ALTER TABLE BoxPackages ADD COLUMN ProductionMode SMALLINT DEFAULT 0 COMMENT ''生产模式：0=正常生产，1=包装返修，2=无检测生产'';',
    'SELECT ''字段BoxPackages.ProductionMode已存在，跳过添加'';'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 为CartonPackages表添加ProductionMode字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'CartonPackages' 
     AND COLUMN_NAME = 'ProductionMode') = 0,
    'ALTER TABLE CartonPackages ADD COLUMN ProductionMode SMALLINT DEFAULT 0 COMMENT ''生产模式：0=正常生产，1=包装返修，2=无检测生产'';',
    'SELECT ''字段CartonPackages.ProductionMode已存在，跳过添加'';'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 为生产模式字段创建索引
-- QualityInspection表索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'QualityInspections' 
     AND INDEX_NAME = 'IX_QualityInspection_ProductionMode') = 0,
    'CREATE INDEX IX_QualityInspection_ProductionMode ON QualityInspections(ProductionMode);',
    'SELECT ''索引IX_QualityInspection_ProductionMode已存在，跳过创建'';'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ProductPhotos表索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'ProductPhotos' 
     AND INDEX_NAME = 'IX_ProductPhotos_ProductionMode') = 0,
    'CREATE INDEX IX_ProductPhotos_ProductionMode ON ProductPhotos(ProductionMode);',
    'SELECT ''索引IX_ProductPhotos_ProductionMode已存在，跳过创建'';'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- BoxPackages表索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'BoxPackages' 
     AND INDEX_NAME = 'IX_BoxPackages_ProductionMode') = 0,
    'CREATE INDEX IX_BoxPackages_ProductionMode ON BoxPackages(ProductionMode);',
    'SELECT ''索引IX_BoxPackages_ProductionMode已存在，跳过创建'';'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- CartonPackages表索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'CartonPackages' 
     AND INDEX_NAME = 'IX_CartonPackages_ProductionMode') = 0,
    'CREATE INDEX IX_CartonPackages_ProductionMode ON CartonPackages(ProductionMode);',
    'SELECT ''索引IX_CartonPackages_ProductionMode已存在，跳过创建'';'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示所有表的结构确认
SELECT 'QualityInspection表结构:' as Info;
DESCRIBE QualityInspections;

SELECT 'ProductPhotos表结构:' as Info;
DESCRIBE ProductPhotos;

SELECT 'BoxPackages表结构:' as Info;
DESCRIBE BoxPackages;

SELECT 'CartonPackages表结构:' as Info;
DESCRIBE CartonPackages;