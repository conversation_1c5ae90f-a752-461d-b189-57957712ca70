using System;

namespace OutletProductionPacking.Data.Models
{
    public class BoxPackage
    {
        public int Id { get; set; }
        public string BoxNumber { get; set; } // 盒号
        public int OrderId { get; set; }
        public int ProductId { get; set; }
        public int BarcodeCount { get; set; } // 包装的条码数量
        public DateTime CreatedAt { get; set; }
        public int OperatorId { get; set; }
        /// <summary>
        /// 生产模式：0=正常生产，1=包装返修，2=无检测生产
        /// </summary>
        public short ProductionMode { get; set; } = 0;
    }
}
