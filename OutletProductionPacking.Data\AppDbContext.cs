using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        public DbSet<User> Users { get; set; } = null!;
        public DbSet<Product> Products { get; set; } = null!;
        public DbSet<ProductionOrder> ProductionOrders { get; set; } = null!;
        public DbSet<ProductionOrderBarcode> ProductionOrderBarcodes { get; set; } = null!;
        public DbSet<QualityInspection> QualityInspections { get; set; } = null!;
        public DbSet<ProductPhoto> ProductPhotos { get; set; } = null!;
        public DbSet<BoxLabelSequence> BoxLabelSequences { get; set; } = null!;
        public DbSet<BoxPackage> BoxPackages { get; set; }
        public DbSet<CartonLabelSequence> CartonLabelSequences { get; set; }
        public DbSet<CartonPackage> CartonPackages { get; set; }
        public DbSet<CartonBoxMapping> CartonBoxMappings { get; set; } = null!;
        public DbSet<BoxQueue> BoxQueue { get; set; } = null!;
        public DbSet<CartonQueue> CartonQueue { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 用户配置
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Password).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.IsActive).IsRequired();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt);

                // 用户名唯一索引
                entity.HasIndex(e => e.Username).IsUnique();
            });

            // 产品配置
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Specification).HasMaxLength(100);
                entity.Property(e => e.ElectricalParameters).HasMaxLength(200);
                entity.Property(e => e.BoxQuantity).HasMaxLength(20);
                entity.Property(e => e.EanCode).HasMaxLength(20);
                entity.Property(e => e.Category).HasMaxLength(50);
                entity.Property(e => e.IsActive).IsRequired();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt);

                // 产品编码唯一索引
                entity.HasIndex(e => e.Code).IsUnique();
            });

            // 生产订单配置
            modelBuilder.Entity<ProductionOrder>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OrderNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.ProductCode).IsRequired().HasMaxLength(50);
                entity.Property(e => e.ProductName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ProductSpecification).HasMaxLength(100);
                entity.Property(e => e.Quantity).IsRequired();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.IsFinished).IsRequired().HasDefaultValue(false);

                // 订单号唯一索引
                entity.HasIndex(e => e.OrderNumber).IsUnique();
            });

            // 生产订单条码配置
            modelBuilder.Entity<ProductionOrderBarcode>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OrderId).IsRequired();
                entity.Property(e => e.Barcode).IsRequired().HasMaxLength(50);
                entity.Property(e => e.IsProduced).IsRequired();
                entity.Property(e => e.BoxNumber).HasMaxLength(50);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                // 条码唯一索引
                entity.HasIndex(e => e.Barcode).IsUnique();
                // 盒号索引
                entity.HasIndex(e => e.BoxNumber);

                // 外键关系
                entity.HasOne(d => d.Order)
                    .WithMany(p => p.Barcodes)
                    .HasForeignKey(d => d.OrderId)
                    .OnDelete(DeleteBehavior.Cascade); // 级联删除
            });

            // 质量检测记录配置
            modelBuilder.Entity<QualityInspection>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Barcode).IsRequired().HasMaxLength(50);
                entity.Property(e => e.OrderId).IsRequired();
                entity.Property(e => e.ProductId).IsRequired();
                entity.Property(e => e.ProductCategory).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Result).IsRequired();
                entity.Property(e => e.DiStatusJson).IsRequired();
                entity.Property(e => e.OperatorId).IsRequired();
                entity.Property(e => e.Remarks).HasMaxLength(500);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                // 创建索引
                entity.HasIndex(e => e.Barcode);
                entity.HasIndex(e => e.OrderId);
            });

            // 成品拍照记录配置
            modelBuilder.Entity<ProductPhoto>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Barcode).IsRequired().HasMaxLength(50);
                entity.Property(e => e.OrderId).IsRequired();
                entity.Property(e => e.PhotoPath).IsRequired().HasMaxLength(500);
                entity.Property(e => e.OperatorId).IsRequired();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.Watermarked).HasDefaultValue((short)0);

                // 创建索引
                entity.HasIndex(e => e.Barcode);
                entity.HasIndex(e => e.OrderId);
                entity.HasIndex(e => e.Watermarked);
            });

            // 小盒贴序号配置
            modelBuilder.Entity<BoxLabelSequence>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.DateKey).IsRequired().HasMaxLength(8);
                entity.Property(e => e.CurrentSequence).IsRequired();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");

                // 日期键唯一索引
                entity.HasIndex(e => e.DateKey).IsUnique();
            });

            // 盒装记录配置
            modelBuilder.Entity<BoxPackage>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.BoxNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.OrderId).IsRequired();
                entity.Property(e => e.ProductId).IsRequired();
                entity.Property(e => e.BarcodeCount).IsRequired();
                entity.Property(e => e.OperatorId).IsRequired();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                // 创建索引
                entity.HasIndex(e => e.BoxNumber).IsUnique();
                entity.HasIndex(e => e.OrderId);
            });

            // 大箱贴序号配置
            modelBuilder.Entity<CartonLabelSequence>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.DateCode).IsRequired().HasMaxLength(6);
                entity.Property(e => e.CurrentSequence).IsRequired();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");

                // 日期码唯一索引
                entity.HasIndex(e => e.DateCode).IsUnique();
            });

            // 大箱包装记录配置
            modelBuilder.Entity<CartonPackage>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.CartonNumber).IsRequired().HasMaxLength(10);
                entity.Property(e => e.OrderId).IsRequired();
                entity.Property(e => e.ProductId).IsRequired();
                entity.Property(e => e.Weight).IsRequired().HasColumnType("decimal(10,1)");
                entity.Property(e => e.TotalQuantity).IsRequired();
                entity.Property(e => e.BoxCount).IsRequired();
                entity.Property(e => e.ProductionDate).IsRequired();
                entity.Property(e => e.OperatorId).IsRequired();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                // 创建索引
                entity.HasIndex(e => e.CartonNumber).IsUnique();
                entity.HasIndex(e => e.OrderId);
                entity.HasIndex(e => e.ProductId);
                entity.HasIndex(e => e.CreatedAt);
            });

            // 大箱与小盒关联配置
            modelBuilder.Entity<CartonBoxMapping>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.CartonPackageId).IsRequired();
                entity.Property(e => e.CartonNumber).IsRequired().HasMaxLength(10);
                entity.Property(e => e.BoxNumber).IsRequired().HasMaxLength(11);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                // 创建索引
                entity.HasIndex(e => e.CartonPackageId);
                entity.HasIndex(e => e.CartonNumber);
                entity.HasIndex(e => e.BoxNumber);
                entity.HasIndex(e => new { e.CartonNumber, e.BoxNumber }).IsUnique();

                // 外键关系
                entity.HasOne(d => d.CartonPackage)
                    .WithMany(p => p.CartonBoxMappings)
                    .HasForeignKey(d => d.CartonPackageId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // 小盒装箱队列配置
            modelBuilder.Entity<BoxQueue>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Barcode).IsRequired().HasMaxLength(50);
                entity.Property(e => e.OrderId).IsRequired();
                entity.Property(e => e.ProductId).IsRequired();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                // 创建索引
                entity.HasIndex(e => e.Barcode).IsUnique();
                entity.HasIndex(e => e.OrderId);
                entity.HasIndex(e => e.ProductId);
                entity.HasIndex(e => e.CreatedAt);

                // 外键关系
                entity.HasOne(d => d.Order)
                    .WithMany()
                    .HasForeignKey(d => d.OrderId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Product)
                    .WithMany()
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // 大箱装箱队列配置
            modelBuilder.Entity<CartonQueue>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.BoxNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.OrderId).IsRequired();
                entity.Property(e => e.ProductId).IsRequired();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                // 创建索引
                entity.HasIndex(e => e.BoxNumber).IsUnique();
                entity.HasIndex(e => e.OrderId);
                entity.HasIndex(e => e.ProductId);
                entity.HasIndex(e => e.CreatedAt);

                // 外键关系
                entity.HasOne(d => d.Order)
                    .WithMany()
                    .HasForeignKey(d => d.OrderId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Product)
                    .WithMany()
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }
    }
}