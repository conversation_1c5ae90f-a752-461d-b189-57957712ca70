using System;
using System.Collections.Generic;
using System.IO;
using Seagull.BarTender.Print;
using BarTenderPrintService.Models;

namespace BarTenderPrintService.Services
{
    /// <summary>
    /// BarTender 打印服务
    /// </summary>
    public class BarTenderService : IDisposable
    {
        private Engine _btEngine;
        private bool _isInitialized = false;
        private readonly string _templateBasePath;
        private readonly object _lockObject = new object();

        public BarTenderService(string templateBasePath)
        {
            _templateBasePath = templateBasePath;
        }

        /// <summary>
        /// 初始化 BarTender 引擎
        /// </summary>
        public bool Initialize()
        {
            if (_isInitialized && _btEngine != null)
                return true;

            lock (_lockObject)
            {
                try
                {
                    Console.WriteLine("正在初始化 BarTender 引擎...");

                    // 尝试不启动 BarTender 应用程序
                    _btEngine = new Engine(true);

                    // 启动引擎 - 这是关键步骤！
                    Console.WriteLine("正在启动 BarTender 引擎...");

                    _isInitialized = true;
                    Console.WriteLine("✅ BarTender 引擎初始化并启动成功");
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ BarTender 引擎初始化失败: {ex.Message}");
                    try
                    {
                        // 清理失败的引擎
                        if (_btEngine != null)
                        {
                            try { _btEngine.Dispose(); } catch { }
                            _btEngine = null;
                        }

                        // 尝试启动 BarTender 应用程序模式
                        Console.WriteLine("尝试启动 BarTender 应用程序模式...");
                        _btEngine = new Engine(true);

                        // 启动引擎
                        Console.WriteLine("正在启动 BarTender 引擎（应用程序模式）...");
                        _btEngine.Start();

                        _isInitialized = true;
                        Console.WriteLine("✅ BarTender 引擎初始化成功（应用程序模式）");
                        return true;
                    }
                    catch (Exception ex2)
                    {
                        Console.WriteLine($"❌ BarTender 引擎初始化完全失败: {ex2.Message}");
                        Console.WriteLine($"详细错误: {ex2}");

                        // 清理失败的引擎
                        if (_btEngine != null)
                        {
                            try { _btEngine.Dispose(); } catch { }
                            _btEngine = null;
                        }

                        return false;
                    }
                }
            }
        }

        /// <summary>
        /// 打印标签
        /// </summary>
        public PrintResponse PrintLabel(PrintRequest request)
        {
            // 检查引擎状态
            if (!_isInitialized || _btEngine == null)
            {
                Console.WriteLine("⚠️ BarTender 引擎未初始化，正在重新初始化...");
                if (!Initialize())
                {
                    return PrintResponse.CreateError(request.RequestId, "BarTender 引擎初始化失败", "ENGINE_NOT_INITIALIZED");
                }
            }

            // 验证引擎是否可用
            try
            {
                if (_btEngine == null)
                {
                    return PrintResponse.CreateError(request.RequestId, "BarTender 引擎为空", "ENGINE_NULL");
                }

                // 简单测试引擎是否可用（通过访问 Documents 属性）
                try
                {
                    var documentsCount = _btEngine.Documents.Count;
                    Console.WriteLine($"🔍 BarTender 引擎正常，当前打开文档数: {documentsCount}");
                }
                catch (Exception testEx)
                {
                    Console.WriteLine($"⚠️ BarTender 引擎可能未正常运行: {testEx.Message}");
                    Console.WriteLine("🔄 尝试重新启动引擎...");

                    try
                    {
                        _btEngine.Start();
                        Console.WriteLine("✅ BarTender 引擎已重新启动");
                    }
                    catch (Exception startEx)
                    {
                        Console.WriteLine($"❌ 重新启动引擎失败: {startEx.Message}");
                        return PrintResponse.CreateError(request.RequestId, $"引擎重启失败: {startEx.Message}", "ENGINE_RESTART_FAILED");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 检查引擎状态失败: {ex.Message}");
                return PrintResponse.CreateError(request.RequestId, $"引擎状态检查失败: {ex.Message}", "ENGINE_STATE_ERROR");
            }

            try
            {
                // 构建模板完整路径
                string templatePath = Path.Combine(_templateBasePath, request.TemplateName);
                if (!File.Exists(templatePath))
                {
                    return PrintResponse.CreateError(request.RequestId, $"模板文件不存在: {templatePath}", "TEMPLATE_NOT_FOUND");
                }

                Console.WriteLine($"🖨️ 开始打印: {request.TemplateName}, 请求ID: {request.RequestId}");
                Console.WriteLine($"📄 模板路径: {templatePath}");

                lock (_lockObject)
                {
                    // 打开模板文档
                    Console.WriteLine("📂 正在打开模板文档...");
                    var btFormat = _btEngine.Documents.Open(templatePath);
                    btFormat.Close(SaveOptions.DoNotSaveChanges);
                    try
                    {
                        Console.WriteLine("⚙️ 正在设置打印参数...");

                        // 设置打印参数
                        foreach (var param in request.Parameters)
                        {
                            try
                            {
                                var subString = btFormat.SubStrings[param.Key];
                                if (subString != null)
                                {
                                    subString.Value = param.Value;
                                    Console.WriteLine($"✅ 设置参数: {param.Key} = {param.Value}");
                                }
                                else
                                {
                                    Console.WriteLine($"⚠️ 警告: 模板中未找到参数 {param.Key}");
                                }
                            }
                            catch (Exception paramEx)
                            {
                                Console.WriteLine($"❌ 设置参数失败 {param.Key}: {paramEx.Message}");
                            }
                        }

                        // 设置打印机
                        if (!string.IsNullOrEmpty(request.PrinterName))
                        {
                            try
                            {
                                btFormat.PrintSetup.PrinterName = request.PrinterName;
                                Console.WriteLine($"🖨️ 设置打印机: {request.PrinterName}");
                            }
                            catch (Exception printerEx)
                            {
                                Console.WriteLine($"⚠️ 设置打印机失败: {printerEx.Message}");
                            }
                        }

                        // 设置打印份数
                        try
                        {
                            btFormat.PrintSetup.NumberOfSerializedLabels = request.Copies;
                            Console.WriteLine($"📊 设置打印份数: {request.Copies}");
                        }
                        catch (Exception copiesEx)
                        {
                            Console.WriteLine($"⚠️ 设置打印份数失败: {copiesEx.Message}");
                        }

                        // 执行打印
                        Console.WriteLine("🚀 正在执行打印...");
                        var result = btFormat.Print();

                        if (result == Result.Success)
                        {
                            Console.WriteLine($"✅ 打印成功: {request.TemplateName}");
                            return PrintResponse.CreateSuccess(request.RequestId, $"打印成功，份数: {request.Copies}");
                        }
                        else
                        {
                            string errorMsg = $"打印失败: {result}";
                            Console.WriteLine($"❌ {errorMsg}");
                            return PrintResponse.CreateError(request.RequestId, errorMsg, "PRINT_FAILED");
                        }
                    }
                    finally
                    {
                        // 关闭文档
                        try
                        {
                            Console.WriteLine("📄 正在关闭模板文档...");
                            btFormat.Close(SaveOptions.DoNotSaveChanges);
                        }
                        catch (Exception closeEx)
                        {
                            Console.WriteLine($"⚠️ 关闭文档失败: {closeEx.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                string errorMsg = $"打印过程中发生异常: {ex.Message}";
                Console.WriteLine($"❌ {errorMsg}");
                Console.WriteLine($"详细错误: {ex}");
                return PrintResponse.CreateError(request.RequestId, errorMsg, "EXCEPTION");
            }
        }

        /// <summary>
        /// 获取服务状态
        /// </summary>
        public bool IsReady()
        {
            return _isInitialized && _btEngine != null;
        }

        /// <summary>
        /// 获取可用的打印机列表
        /// </summary>
        public List<string> GetAvailablePrinters()
        {
            var printers = new List<string>();
            try
            {
                foreach (string printerName in System.Drawing.Printing.PrinterSettings.InstalledPrinters)
                {
                    printers.Add(printerName);
                }
                Console.WriteLine($"🖨️ 找到 {printers.Count} 个打印机");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 获取打印机列表失败: {ex.Message}");
                // 添加默认打印机作为备选
                printers.Add("Microsoft Print to PDF");
            }
            return printers;
        }

        /// <summary>
        /// 验证模板文件是否存在
        /// </summary>
        public bool ValidateTemplate(string templateName)
        {
            string templatePath = Path.Combine(_templateBasePath, templateName);
            return File.Exists(templatePath);
        }

        public void Dispose()
        {
            try
            {
                if (_btEngine != null)
                {
                    _btEngine.Stop(SaveOptions.DoNotSaveChanges);
                    _btEngine.Dispose();
                    _btEngine = null;
                    _isInitialized = false;
                    Console.WriteLine("BarTender 引擎已释放");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"释放 BarTender 引擎时发生错误: {ex.Message}");
            }
        }
    }
}
