<UserControl x:Class="OutletProductionPacking.WPF.Views.UserControls.WorkspaceView" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:local="clr-namespace:OutletProductionPacking.WPF.Views.UserControls"
 mc:Ignorable="d" Background="{StaticResource BackgroundBrush}" d:DesignHeight="1000" d:DesignWidth="1920">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <!-- 顶部：基础信息与硬件状态 -->
            <RowDefinition Height="7*"/>
            <!-- 中部上：质量检测和成品拍照 -->
            <RowDefinition Height="3*"/>
            <!-- 中部下：小盒贴打印和大箱称重 -->
            <RowDefinition Height="Auto"/>
            <!-- 底部：状态与日志 -->
        </Grid.RowDefinitions>

        <!-- 顶部：基础信息与硬件状态 -->
        <Grid Grid.Row="0" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <!-- 操作工登录 -->
                <ColumnDefinition Width="*"/>
                <!-- 订单选择 -->
                <ColumnDefinition Width="Auto"/>
                <!-- 硬件状态 -->
            </Grid.ColumnDefinitions>

            <!-- 操作工登录区域 -->
            <GroupBox Grid.Column="0" Header="操作工登录" Margin="0,0,10,0" Width="250">
                <Grid Margin="5">
                    <!-- 登录表单 - 未登录时显示 -->
                    <Grid Visibility="{Binding IsLoggedIn, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="用户名：" VerticalAlignment="Center" Margin="0,0,5,5"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="密码：" VerticalAlignment="Center" Margin="0,0,5,5"/>
                        <PasswordBox Grid.Row="1" Grid.Column="1" x:Name="PasswordBox" Margin="0,0,0,5"/>

                        <Button Grid.Row="2" Grid.ColumnSpan="2" Content="登录" Command="{Binding LoginCommand}"
                                Style="{StaticResource PrimaryButtonStyle}" HorizontalAlignment="Right" Margin="0,5,0,0"/>
                    </Grid>

                    <!-- 已登录信息 - 登录后显示 -->
                    <Grid Visibility="{Binding IsLoggedIn, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" HorizontalAlignment="Center" Margin="0,10,0,15">
                            <Run Text="当前操作工：" FontSize="14"/>
                            <Run Text="{Binding CurrentUser.Name}" FontSize="16" FontWeight="Bold" Foreground="Blue"/>
                        </TextBlock>

                        <Button Grid.Row="1" Content="登出" Command="{Binding LogoutCommand}"
                                Style="{StaticResource DefaultButtonStyle}" HorizontalAlignment="Center" Margin="0,5,0,10"/>
                    </Grid>
                </Grid>
            </GroupBox>

            <!-- 订单选择区域 -->
            <GroupBox Grid.Column="1" Header="订单信息" Margin="0,0,10,0">
                <Grid Margin="5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="选择订单：" VerticalAlignment="Center" Margin="0,0,5,5"/>
                    <WrapPanel>
                        <ComboBox Grid.Row="0" Grid.Column="1" ItemsSource="{Binding AvailableOrders}" SelectedItem="{Binding SelectedOrder}" Width="500" HorizontalAlignment="Left">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock>
                                        <Run Text="{Binding OrderNumber}" FontWeight="Bold"/>
                                        <Run Text=" | "/>
                                        <Run Text="{Binding ProductCode}"/>
                                        <Run Text=" | "/>
                                        <Run Text="{Binding ProductName}"/>
                                        <Run Text=" | 数量:"/>
                                        <Run Text="{Binding Quantity}"/>
                                        </TextBlock>
                                    </StackPanel>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>
                        <Button Grid.Row="0" Grid.Column="1" Width="100" Content="确认" Command="{Binding SelectOrderCommand}" Style="{StaticResource PrimaryButtonStyle}"/>

                        <!-- 生产模式选择 -->
                        <TextBlock Text="生产模式：" VerticalAlignment="Center" Margin="20,0,5,0"/>
                        <ComboBox ItemsSource="{Binding ProductionModes}" SelectedItem="{Binding SelectedProductionMode}" Width="120" HorizontalAlignment="Left"/>

                        <!-- 总入队计数器 -->
                        <TextBlock Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="20,0,0,0">
                            <Run Text="总入队数：" FontSize="18" Foreground="DarkGreen"/>
                            <Run Text="{Binding TotalEnqueuedCount}" FontWeight="Bold" Foreground="Red" FontSize="22"/>
                        </TextBlock>
                        <!-- DI9信号计数 -->
                        <TextBlock Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="20,0,0,0">
                            <Run Text="DI9计数：" FontSize="18" Foreground="DarkGreen"/>
                            <Run Text="{Binding TotalDI9Count}" FontWeight="Bold" Foreground="Red" FontSize="22"/>
                        </TextBlock>
                        <!-- DO4信号计数 -->
                        <TextBlock Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="20,0,0,0">
                            <Run Text="DO4计数：" FontSize="18" Foreground="DarkGreen"/>
                            <Run Text="{Binding TotalDO4Count}" FontWeight="Bold" Foreground="Red" FontSize="22"/>
                        </TextBlock>
                        <!-- 扫到码计数 -->
                        <TextBlock Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="20,0,0,0">
                            <Run Text="扫码计数：" FontSize="18" Foreground="DarkGreen"/>
                            <Run Text="{Binding TotalBarCodeCount}" FontWeight="Bold" Foreground="Red" FontSize="22"/>
                        </TextBlock>
                    </WrapPanel>
                    <TextBlock Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3" Margin="0,5,0,5">
                        <Run Text="产品："/>
                        <Run Text="{Binding CurrentProduct.Name, TargetNullValue='未选择'}"/>
                        <Run Text=" | 规格："/>
                        <Run Text="{Binding CurrentProduct.Specification, TargetNullValue='--'}"/>
                        <Run Text=" | 类别："/>
                        <Run Text="{Binding CurrentProduct.Category, TargetNullValue='--'}"/>
                    </TextBlock>

                    <Grid Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="生产进度：" VerticalAlignment="Center"/>
                        <ProgressBar Grid.Column="1" Value="{Binding ProgressPercentage}" Maximum="100" Height="15" Margin="5,0"/>
                        <TextBlock Grid.Column="2" Margin="0,0,5,0">
                            <Run Text="{Binding CompletedQuantity}"/>
                            <Run Text="/"/>
                            <Run Text="{Binding TotalOrderQuantity}"/>
                        </TextBlock>
                        <TextBlock Grid.Column="3">
                            <Run Text="{Binding ProgressPercentage, StringFormat={}{0:F1}}"/>
                            <Run Text="%"/>
                        </TextBlock>
                    </Grid>
                </Grid>
            </GroupBox>


        </Grid>

        <!-- 中部上：质量检测和成品拍照 -->
        <Grid Grid.Row="1" Margin="10,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 质量检测工序区域 -->
            <GroupBox Grid.Column="0" Header="质量检测工序" Margin="0,0,5,0">
                <Grid Margin="5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 条码显示 -->
                    <TextBlock Grid.Row="0" Margin="0,0,0,5" VerticalAlignment="Center" >
                        <Run Text="当前条码：" FontSize="20" />
                        <Run Text="{Binding QualityBarcode}" FontWeight="Bold" Foreground="Blue" FontSize="20"/>
                    </TextBlock>

                    <!-- 检测状态 -->
                    <TextBlock Grid.Row="0" Margin="560,0,0,5">
                        <Run Text="检测状态：" FontSize="20"/>
                        <Run Text="{Binding QualityStatus}" FontWeight="Bold" Foreground="Blue" FontSize="20"/>
                    </TextBlock>

                    <!-- 检测指示灯和合格状态并排显示 -->
                    <Grid Margin="2" Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- 检测指示灯 -->
                        <ItemsControl Grid.Column="0" ItemsSource="{Binding DiStatus}" AlternationCount="16"
                                          VerticalAlignment="Center" Margin="0,0,5,0">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Width="46" Height="45" Margin="1"
                                                Background="{Binding Converter={StaticResource BoolToBrushConverter}}">
                                        <TextBlock Text="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type ContentPresenter}}, Path=(ItemsControl.AlternationIndex)}"
                                                       HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="11" FontWeight="Bold"/>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>

                        <!-- 合格状态 -->
                        <Border Grid.Column="1" Background="{Binding IsQualityPassed, Converter={StaticResource BoolToBrushConverter}}"
                                    Width="140" Height="45" VerticalAlignment="Center">
                            <TextBlock Text="{Binding IsQualityPassed, Converter={StaticResource BoolToQualityStatusConverter}}"
                                           HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Bold" FontSize="14"/>
                        </Border>
                    </Grid>

                    <!-- 检测历史记录 -->
                    <GroupBox Grid.Row="3" Header="最近检测历史">
                        <DataGrid ItemsSource="{Binding RecentInspections}" AutoGenerateColumns="False" IsReadOnly="True" HeadersVisibility="Column"
                            GridLinesVisibility="Horizontal" RowHeight="25" FontSize="11" Margin="2">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="采购订单号" Binding="{Binding OrderNumber}" Width="120"/>
                                <DataGridTextColumn Header="规格型号" Binding="{Binding ProductSpecification}" Width="120"/>
                                <DataGridTextColumn Header="产品类别" Binding="{Binding ProductCategory}" Width="80"/>
                                <DataGridTextColumn Header="条码" Binding="{Binding Barcode}" Width="*"/>
                                <DataGridTextColumn Header="时间" Binding="{Binding FormattedTime}" Width="80"/>
                                <DataGridTextColumn Header="结果" Binding="{Binding ResultText}" Width="60">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Result}" Value="True">
                                                    <Setter Property="Foreground" Value="Green"/>
                                                    <Setter Property="FontWeight" Value="Bold"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Result}" Value="False">
                                                    <Setter Property="Foreground" Value="Red"/>
                                                    <Setter Property="FontWeight" Value="Bold"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </GroupBox>

                    <!-- 提交按钮 -->
                    <Button Grid.Row="4" Content="提交当前条码结果" Command="{Binding SubmitQualityResultCommand}" Visibility="Collapsed"
                            Style="{StaticResource PrimaryButtonStyle}" HorizontalAlignment="Right" Margin="0,5,0,0"/>
                </Grid>
            </GroupBox>

            <!-- 成品拍照工序区域 -->
            <GroupBox Grid.Column="1" Header="成品拍照工序" Margin="5,0,0,0">
                <Grid Margin="5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 第一行：条码显示和拍照状态 -->
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>

                        <!-- 拍照状态 -->
                        <TextBlock Grid.Column="1">
                            <Run Text="拍照状态：" FontSize="20"/>
                            <Run Text="{Binding PhotoStatus}" FontWeight="Bold" Foreground="Blue" FontSize="20" />
                        </TextBlock>
                        <!-- 队列状态显示 -->
                        <TextBlock Grid.Column="0" >
                          <Run Text="队列状态：" FontSize="20"/>
                          <Run Text="{Binding QueueStatus}" FontWeight="Bold" Foreground="Green" FontSize="20"/>
                        </TextBlock>

                    </Grid>
                    <!-- 队列和历史记录并排显示 -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="350"/>
                            <ColumnDefinition Width="55*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 左侧：待拍照队列 -->
                        <GroupBox Grid.Column="0" Header="{Binding QueueCount, StringFormat='📋 待拍照队列 ({0}个)'}" Margin="0,0,5,0">
                            <!-- 队列列表 -->
                            <DataGrid ItemsSource="{Binding PhotoQueue}" AutoGenerateColumns="False" IsReadOnly="True" SelectionMode="Single" SelectedItem="{Binding SelectedPhotoSerialBarCode}"
                                      HeadersVisibility="Column" GridLinesVisibility="Horizontal" RowHeight="25" FontSize="11" Margin="2">
                                <DataGrid.ContextMenu>
                                    <ContextMenu>
                                        <MenuItem Header="删除选中行" Command="{Binding RemoveSelectedItemCommand}" />
                                    </ContextMenu>
                                </DataGrid.ContextMenu>
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="入队时间" Binding="{Binding DisplayTime}" Width="80"/>
                                    <DataGridTextColumn Header="条码号" Binding="{Binding Barcode}" Width="*"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </GroupBox>

                        <!-- 右侧：拍照历史 -->
                        <GroupBox Grid.Column="1" Header="📷 拍照历史" Margin="5,0,0,0">
                            <DataGrid ItemsSource="{Binding RecentPhotos}" AutoGenerateColumns="False" IsReadOnly="True"
                                      HeadersVisibility="Column" GridLinesVisibility="Horizontal" RowHeight="25" FontSize="11" Margin="2">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="拍照时间" Binding="{Binding FormattedTime}" Width="80"/>
                                    <DataGridTextColumn Header="条码号" Binding="{Binding Barcode}" Width="180"/>
                                    <DataGridTemplateColumn Header="照片路径" Width="*">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Button Content="{Binding DisplayPath}" Command="{Binding DataContext.ViewPhotoCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding PhotoPath}" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" FontSize="10" Padding="2"
                                                    HorizontalContentAlignment="Left" ToolTip="{Binding PhotoPath}" IsEnabled="{Binding HasPhoto}"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </GroupBox>
                    </Grid>

                    <!-- 底部按钮区域：线体状态提示、恢复按钮和提交按钮 -->
                    <Grid Grid.Row="2" Margin="0,5,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- 线体状态提示 -->
                        <TextBlock Grid.Column="0" VerticalAlignment="Center" Foreground="Red" FontWeight="Bold" FontSize="22" Visibility="{Binding HasPhotoError, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Run Text="{Binding PhotoErrorMessage}"/><!--⚠️ 线体已停止：请取走产品-->
                        </TextBlock>

                        <!-- 队列操作和恢复线体按钮 -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="0,0,10,0">
                            <!--<Button Content="清空队列" Command="{Binding ClearPhotoQueueCommand}"
                                    Background="Orange" Foreground="White" FontWeight="Bold" Padding="8,5" Margin="0,0,5,0"/>
                            <Button Content="移除队首" Command="{Binding RemoveFirstQueueItemCommand}"
                                    Background="Red" Foreground="White" FontWeight="Bold" Padding="8,5" Margin="0,0,10,0"/>-->
                            <Button Content="测试停线" Command="{Binding TestStopLineCommand}" Background="Orange" Foreground="White" FontWeight="Bold" Padding="10,5" ToolTip="点击恢复线体运行（DO0=False）"/>
                            <Button Content="手动恢复线体" Command="{Binding RestoreLineCommand}" Background="Orange" Foreground="White" FontWeight="Bold" Padding="10,5" ToolTip="点击恢复线体运行（DO0=False）"/>
                        </StackPanel>

                        <!-- 提交按钮和测试按钮 -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                            <!--<Button Content="测试停线" Command="{Binding TestStopLineCommand}"
                                    Background="Red" Foreground="White" FontWeight="Bold" Padding="10,5" Margin="0,0,10,0"/>-->
                            <!--<Button Content="重置计数" Command="{Binding ResetTotalEnqueuedCountCommand}"
                                    Background="Orange" Foreground="White" FontWeight="Bold" Padding="10,5" Margin="0,0,10,0"/>-->
                            <!--<Button Content="提交当前条码结果" Command="{Binding SubmitPhotoResultCommand}" Visibility="Collapsed"
                                    Style="{StaticResource PrimaryButtonStyle}"/>-->
                        </StackPanel>
                    </Grid>
                </Grid>
            </GroupBox>
        </Grid>

        <!-- 中部下：小盒贴打印和大箱称重 -->
        <Grid Grid.Row="2" Margin="10,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="41*"/>
                <ColumnDefinition Width="909*"/>
                <ColumnDefinition Width="950*"/>
            </Grid.ColumnDefinitions>

            <!-- 小盒贴打印工序区域 -->
            <GroupBox Grid.Column="0" Header="小盒贴打印工序" Margin="0,0,5,0" Grid.ColumnSpan="2">
                <Grid Margin="5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 计数显示 -->
                    <TextBlock Grid.Row="0" Margin="0,0,450,5">
                        <Run Text="当前计数：" FontSize="20"/>
                        <Run Text="{Binding BoxCount}" FontWeight="Bold" FontSize="20"/>
                        <Run Text="/" FontSize="20"/>
                        <Run Text="{Binding BoxCapacity}" FontWeight="Bold" FontSize="20"/>
                    </TextBlock>

                    <!-- 状态显示 -->
                    <TextBlock Grid.Row="0" Margin="500,0,0,0">
                        <Run Text="状态：" FontSize="20"/>
                        <Run Text="{Binding BoxStatus}" FontWeight="Bold" FontSize="20"/>
                    </TextBlock>
                    <Border Grid.Row="2" BorderBrush="LightGray" BorderThickness="1" Margin="0,0,0,5">
                        <Grid Grid.Row="2">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <TextBlock Grid.Row="0" Margin="5,5,5,0" FontSize="12" Foreground="DarkBlue"
           Text="条码列表：" FontWeight="Bold"/>
                            <!-- 条码列表 -->
                            <ListBox Grid.Row="2" ItemsSource="{Binding CurrentBoxBarcodes}"
                             Margin="0,0,0,5" BorderThickness="0" Background="Transparent">
                                <ListBox.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <WrapPanel Orientation="Horizontal"/>
                                    </ItemsPanelTemplate>
                                </ListBox.ItemsPanel>
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="LightBlue" CornerRadius="3" Padding="5,2" Margin="2">
                                            <TextBlock Text="{Binding}" FontSize="10" FontWeight="Bold"/>
                                        </Border>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </Grid>
                    </Border>
                    <!-- 操作按钮 -->
                    <Button Grid.Row="3" Content="手动打印小盒贴" Command="{Binding PrintBoxLabelCommand}"
                            Style="{StaticResource PrimaryButtonStyle}" HorizontalAlignment="Right"/>
                </Grid>
            </GroupBox>

            <!-- 大箱称重与大箱贴打印工序区域 -->
            <GroupBox Grid.Column="2" Header="大箱称重与大箱贴打印工序" Margin="5,0,0,0">
                <Grid Margin="5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 待装盒数显示 -->
                    <TextBlock Grid.Row="0" Margin="0,0,650,5">
                        <Run Text="待装盒数：" FontSize="20"/>
                        <Run Text="{Binding CartonQueueCount}" FontWeight="Bold" FontSize="20"/>
                        <Run Text="/" FontSize="20"/>
                        <Run Text="{Binding CartonCapacity}" FontWeight="Bold" FontSize="20"/>
                    </TextBlock>

                    <!-- 重量显示 -->
                    <TextBlock Grid.Row="0" Margin="700,0,0,5" FontSize="16" FontWeight="Bold">
                        <Run Text="当前重量：" FontSize="20"/>
                        <Run Text="{Binding CurrentWeight, StringFormat=N3}" FontSize="20"/>
                        <Run Text="{Binding CurrentUnit}" FontSize="20"/>
                    </TextBlock>

                    <!-- 状态显示 -->
                    <TextBlock Grid.Row="0" Margin="300,0,300,5">
                        <Run Text="状态：" FontSize="20"/>
                        <Run Text="{Binding CartonStatus}" FontWeight="Bold" FontSize="20"/>
                    </TextBlock>

                    <!-- 大箱信息区域 - 显示盒号队列 -->
                    <Border Grid.Row="1" BorderBrush="LightGray" BorderThickness="1" Margin="0,0,0,5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- 队列标题 -->
                            <TextBlock Grid.Row="0" Margin="5,5,5,0" FontSize="12" Foreground="DarkBlue"
                                       Text="盒号列表：" FontWeight="Bold"/>

                            <!-- 盒号列表 -->
                            <ListBox Grid.Row="1" ItemsSource="{Binding CartonQueueBoxNumbers}"
                                     Margin="5" BorderThickness="0" Background="Transparent">
                                <ListBox.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <WrapPanel Orientation="Horizontal"/>
                                    </ItemsPanelTemplate>
                                </ListBox.ItemsPanel>
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="LightBlue" CornerRadius="3" Padding="5,2" Margin="2">
                                            <TextBlock Text="{Binding}" FontSize="10" FontWeight="Bold"/>
                                        </Border>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </Grid>
                    </Border>

                    <!-- 操作按钮区域 -->
                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- 自动打印状态提示 -->
                        <TextBlock Grid.Column="0" Text="{Binding AutoPrintStatus}"
                                   VerticalAlignment="Center" FontWeight="Bold" Foreground="Blue"
                                   Visibility="{Binding AutoPrintStatus, Converter={StaticResource StringToVisibilityConverter}}"/>

                        <!-- 手动打印按钮（隐藏但保留） -->
                        <Button Grid.Column="1" Content="打印大箱贴" Command="{Binding PrintCartonLabelCommand}"
                                Style="{StaticResource PrimaryButtonStyle}" HorizontalAlignment="Right"
                                Visibility="Collapsed"/>
                    </Grid>
                </Grid>
            </GroupBox>
        </Grid>

        <!-- 底部：状态与日志 -->
        <GroupBox Grid.Row="3" Header="状态与日志" Margin="10,5,355,10">
            <ListBox ItemsSource="{Binding StatusMessages}" Height="100" Margin="5"/>
        </GroupBox>
        <!-- 硬件状态区域 -->
        <GroupBox Grid.Row="3" Header="硬件状态" Width="340" Height="140" HorizontalAlignment="Right" Margin="0,5,10,10">
            <Grid Height="80" >
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="质检扫码枪：" VerticalAlignment="Center" Margin="0,0,5,5"/>
                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding IsScanner1Connected, Converter={StaticResource BoolToConnectStatusConverter}}" VerticalAlignment="Center" Margin="0,0,0,5"/>

                <TextBlock Grid.Row="0" Grid.Column="2" Text="拍照扫码枪：" VerticalAlignment="Center" Margin="0,0,5,5"/>
                <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding IsScanner2Connected, Converter={StaticResource BoolToConnectStatusConverter}}" VerticalAlignment="Center" Margin="0,0,0,5"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="IO模块：" VerticalAlignment="Center" Margin="0,0,5,5"/>
                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding IsModbusConnected, Converter={StaticResource BoolToConnectStatusConverter}}" VerticalAlignment="Center" Margin="0,0,0,5"/>

                <TextBlock Grid.Row="1" Grid.Column="2" Text="电子秤：" VerticalAlignment="Center" Margin="0,0,5,5"/>
                <TextBlock Grid.Row="1" Grid.Column="3" Text="{Binding IsScaleConnected, Converter={StaticResource BoolToConnectStatusConverter}}" VerticalAlignment="Center" Margin="0,0,0,5"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="相机：" VerticalAlignment="Center" Margin="0,0,5,5"/>
                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding IsCameraConnected, Converter={StaticResource BoolToConnectStatusConverter}}" VerticalAlignment="Center" Margin="0,0,0,5"/>
            </Grid>
        </GroupBox>
    </Grid>
</UserControl>