using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Core.Services
{
    public interface IProductionOrderService
    {
        Task<List<ProductionOrder>> GetAllOrdersAsync();
        Task<List<ProductionOrder>> GetPagedOrdersAsync(int pageNumber, int pageSize, string searchTerm = null);
        Task<List<ProductionOrder>> GetPagedOrdersAsync(int pageNumber, int pageSize, OrderSearchParams searchParams);
        Task<int> GetTotalOrderCountAsync(string searchTerm = null);
        Task<int> GetTotalOrderCountAsync(OrderSearchParams searchParams);
        Task<ProductionOrder?> GetOrderByIdAsync(int id);
        Task<List<ProductionOrderBarcode>> GetBarcodesByOrderIdAsync(int orderId);
        Task<ProductionOrderBarcode?> GetBarcodeAsync(string barcode);
        Task<(bool success, string message, ProductionOrder? order)> ImportFromExcelAsync(string filePath, IProgress<int>? progress = null);
        Task<(bool success, string message, List<ProductionOrder> orders)> ImportFromExcelFilesAsync(string[] filePaths, IProgress<(int fileIndex, int fileCount, int progress)>? progress = null);
        Task<(bool success, string message, ProductionOrder? order, bool needsProductCreation, string productCode, string suggestedProductName, string suggestedSpecification)> ImportFromSheetAsync(string filePath, string sheetName, IProgress<int>? progress = null);
        Task<(bool success, string message, ProductionOrder? order, bool needsProductCreation, string productCode, string suggestedProductName, string suggestedSpecification)> ImportFromSheetAsync(string filePath, string sheetName, IProgress<(int progress, string orderNumber, string productCode, string productName, int barcodeIndex, int barcodeCount)>? detailedProgress);
        Task<bool> DeleteOrderAsync(int id);
        Task<List<ProductionOrder>> GetUncompletedOrdersAsync();
        Task<int> GetCompletedQuantityAsync(int orderId);
        Task UpdateOrderFinishedStatusAsync(int orderId);
        Task MarkBarcodeAsProducedAsync(string barcode);
        Task<bool> IsBarcodeInspectedAsync(string barcode);
        Task<bool?> GetBarcodeQualityStatusAsync(string barcode);
        Task SaveQualityInspectionAsync(string barcode, int id1, int id2, string category, bool result, string diStatusJson, int id3, string empty);
        Task UpdateQualityInspectionResultAsync(string barcode, bool result, string diStatusJson = null);
        Task<bool> HasQualityPassedAsync(string barcode);
        Task SaveProductPhotoAsync(string barcode, int orderId, string photoPath, int operatorId);
        Task<string> GenerateBoxNumberAsync();
        Task SaveBoxPackageAsync(string boxNumber, int orderId, int productId, List<string> barcodes, int operatorId);

        // 大箱相关方法
        Task<string> GenerateCartonNumberAsync();
        Task<bool> SaveCartonPackageAsync(string cartonNumber, int orderId, int productId, List<string> boxNumbers, decimal weight, int operatorId);
        Task<List<string>> GetCartonQueueBoxNumbersAsync(int orderId, int count = 2);
        Task<List<string>> GetBarcodesByBoxNumberAsync(string boxNumber);

        // 小盒队列相关方法
        Task AddBarcodeToBoxQueueAsync(string barcode, int orderId, int productId);
        Task<List<string>> GetBoxQueueBarcodesAsync(int orderId);
        Task RemoveBarcodesFromBoxQueueAsync(List<string> barcodes);
        Task<string?> GetBoxNumberByBarCode(string barcode);
        // 大箱队列相关方法
        Task AddBoxToCartonQueueAsync(string boxNumber, int orderId, int productId);
        Task<List<string>> GetCartonQueueBoxNumbersAsync(int orderId);
        Task RemoveBoxNumbersFromCartonQueueAsync(List<string> boxNumbers);
        Task<List<string>> GetBoxNumbersForCartonPackingAsync(int orderId, int count = 2);
    }
}
