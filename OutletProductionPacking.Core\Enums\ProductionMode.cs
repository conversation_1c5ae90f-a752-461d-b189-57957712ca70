namespace OutletProductionPacking.Core.Enums
{
    /// <summary>
    /// 生产模式枚举
    /// </summary>
    public enum ProductionMode
    {
        /// <summary>
        /// 正常生产模式
        /// </summary>
        Normal = 0,
        
        /// <summary>
        /// 包装返修模式
        /// </summary>
        PackageRepair = 1,
        
        /// <summary>
        /// 无检测生产模式
        /// </summary>
        NoInspection = 2
    }
    
    /// <summary>
    /// 生产模式扩展方法
    /// </summary>
    public static class ProductionModeExtensions
    {
        /// <summary>
        /// 获取生产模式的显示名称
        /// </summary>
        public static string GetDisplayName(this ProductionMode mode)
        {
            return mode switch
            {
                ProductionMode.Normal => "正常生产",
                ProductionMode.PackageRepair => "包装返修",
                ProductionMode.NoInspection => "无检测生产",
                _ => "未知模式"
            };
        }
        
        /// <summary>
        /// 从显示名称获取生产模式
        /// </summary>
        public static ProductionMode FromDisplayName(string displayName)
        {
            return displayName switch
            {
                "正常生产" => ProductionMode.Normal,
                "包装返修" => ProductionMode.PackageRepair,
                "无检测生产" => ProductionMode.NoInspection,
                _ => ProductionMode.Normal
            };
        }
    }
}
