using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Data.Models;
using OutletProductionPacking.Data.Repositories;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace OutletProductionPacking.Services
{
    public class ProductionOrderService : IProductionOrderService
    {
        private readonly IProductionOrderRepository _orderRepository;
        private readonly IProductionOrderBarcodeRepository _barcodeRepository;
        private readonly IProductService _productService;
        private readonly IQualityInspectionRepository _qualityInspectionRepository;
        private readonly IProductPhotoRepository _productPhotoRepository;
        private readonly IBoxLabelSequenceRepository _boxLabelSequenceRepository;
        private readonly IBoxPackageRepository _boxPackageRepository;
        private readonly ICartonLabelSequenceRepository _cartonLabelSequenceRepository;
        private readonly ICartonPackageRepository _cartonPackageRepository;
        private readonly IBoxQueueRepository _boxQueueRepository;
        private readonly ICartonQueueRepository _cartonQueueRepository;

        public ProductionOrderService(
            IProductionOrderRepository orderRepository,
            IProductionOrderBarcodeRepository barcodeRepository,
            IProductService productService,
            IQualityInspectionRepository qualityInspectionRepository,
            IProductPhotoRepository productPhotoRepository,
            IBoxLabelSequenceRepository boxLabelSequenceRepository,
            IBoxPackageRepository boxPackageRepository,
            ICartonLabelSequenceRepository cartonLabelSequenceRepository,
            ICartonPackageRepository cartonPackageRepository,
            IBoxQueueRepository boxQueueRepository,
            ICartonQueueRepository cartonQueueRepository)
        {
            _orderRepository = orderRepository;
            _barcodeRepository = barcodeRepository;
            _productService = productService;
            _qualityInspectionRepository = qualityInspectionRepository;
            _productPhotoRepository = productPhotoRepository;
            _boxLabelSequenceRepository = boxLabelSequenceRepository;
            _boxPackageRepository = boxPackageRepository;
            _cartonLabelSequenceRepository = cartonLabelSequenceRepository;
            _cartonPackageRepository = cartonPackageRepository;
            _boxQueueRepository = boxQueueRepository;
            _cartonQueueRepository = cartonQueueRepository;
        }

        public async Task<List<ProductionOrder>> GetAllOrdersAsync()
        {
            return await _orderRepository.GetAllAsync();
        }

        public async Task<List<ProductionOrder>> GetPagedOrdersAsync(int pageNumber, int pageSize, string searchTerm = null)
        {
            return await _orderRepository.GetPagedAsync(pageNumber, pageSize, searchTerm);
        }

        public async Task<List<ProductionOrder>> GetPagedOrdersAsync(int pageNumber, int pageSize, OrderSearchParams searchParams)
        {
            return await _orderRepository.GetPagedAsync(pageNumber, pageSize, searchParams);
        }

        public async Task<int> GetTotalOrderCountAsync(string searchTerm = null)
        {
            return await _orderRepository.GetTotalCountAsync(searchTerm);
        }

        public async Task<int> GetTotalOrderCountAsync(OrderSearchParams searchParams)
        {
            return await _orderRepository.GetTotalCountAsync(searchParams);
        }

        public async Task<List<ProductionOrderBarcode>> GetBarcodesByOrderIdAsync(int orderId)
        {
            return await _barcodeRepository.GetByOrderIdAsync(orderId);
        }

        public async Task<ProductionOrderBarcode?> GetBarcodeAsync(string barcode)
        {
            try
            {
                return await _barcodeRepository.GetByBarcodeAsync(barcode);
            }
            catch
            {
                return null;
            }
        }

        public async Task<(bool success, string message, ProductionOrder? order)> ImportFromExcelAsync(string filePath, IProgress<int>? progress = null)
        {
            try
            {
                // 验证文件存在
                if (!File.Exists(filePath))
                {
                    return (false, $"文件不存在: {filePath}", null);
                }

                try
                {
                    // 读取Excel文件
                    IWorkbook workbook;
                    using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                    {
                        // 根据文件扩展名决定使用哪种工作簿
                        string extension = Path.GetExtension(filePath).ToLower();
                        if (extension == ".xlsx")
                        {
                            workbook = new XSSFWorkbook(fileStream);
                        }
                        else if (extension == ".xls")
                        {
                            workbook = new HSSFWorkbook(fileStream);
                        }
                        else
                        {
                            return (false, $"文件格式不支持: {Path.GetFileName(filePath)}\n请使用 .xlsx 或 .xls 格式的Excel文件", null);
                        }
                    }

                    using (workbook)
                    {
                        // 如果没有工作表，返回错误
                        if (workbook.NumberOfSheets == 0)
                        {
                            return (false, $"文件中没有工作表: {Path.GetFileName(filePath)}", null);
                        }

                        // 处理第一个工作表
                        ISheet sheet = workbook.GetSheetAt(0);
                        string sheetName = workbook.GetSheetName(0);
                        string fileName = Path.GetFileName(filePath);

                        var result = await ProcessSheetAsync(sheet, sheetName, fileName, progress);
                        return (result.success, result.message, result.order);
                    }
                }
                catch (IOException ex)
                {
                    return (false, $"文件访问错误: {Path.GetFileName(filePath)}\n{ex.Message}", null);
                }
                catch (Exception ex)
                {
                    return (false, $"读取Excel文件失败: {Path.GetFileName(filePath)}\n{ex.Message}", null);
                }
            }
            catch (Exception ex)
            {
                return (false, $"导入失败: {ex.Message}", null);
            }
        }

        public async Task<(bool success, string message, List<ProductionOrder> orders)> ImportFromExcelFilesAsync(
            string[] filePaths,
            IProgress<(int fileIndex, int fileCount, int progress)>? progress = null)
        {
            var results = new List<ProductionOrder>();
            var errors = new List<string>();
            int totalSheets = 0;
            int successSheets = 0;

            for (int i = 0; i < filePaths.Length; i++)
            {
                var filePath = filePaths[i];
                string fileName = Path.GetFileName(filePath);

                try
                {
                    // 验证文件存在
                    if (!File.Exists(filePath))
                    {
                        errors.Add($"文件不存在: {fileName}");
                        continue;
                    }

                    // 读取Excel文件
                    IWorkbook workbook;
                    try
                    {
                        using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                        {
                            // 根据文件扩展名决定使用哪种工作簿
                            string extension = Path.GetExtension(filePath).ToLower();
                            if (extension == ".xlsx")
                            {
                                workbook = new XSSFWorkbook(fileStream);
                            }
                            else if (extension == ".xls")
                            {
                                workbook = new HSSFWorkbook(fileStream);
                            }
                            else
                            {
                                errors.Add($"文件格式不支持: {fileName}\n请使用 .xlsx 或 .xls 格式的Excel文件");
                                continue;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"打开文件失败: {fileName}\n{ex.Message}");
                        continue;
                    }

                    using (workbook)
                    {
                        int sheetCount = workbook.NumberOfSheets;
                        totalSheets += sheetCount;

                        if (sheetCount == 0)
                        {
                            errors.Add($"文件中没有工作表: {fileName}");
                            continue;
                        }

                        // 遍历每个工作表
                        for (int sheetIndex = 0; sheetIndex < sheetCount; sheetIndex++)
                        {
                            ISheet sheet = workbook.GetSheetAt(sheetIndex);
                            string sheetName = workbook.GetSheetName(sheetIndex);

                            // 创建一个进度报告转换器
                            IProgress<int> sheetProgress = null;

                            // 如果需要详细进度报告
                            if (progress != null)
                            {
                                sheetProgress = new Progress<int>(p =>
                                {
                                    // 计算总进度：文件进度 + 工作表进度
                                    double fileProgress = (i * 100.0) / filePaths.Length;
                                    double sheetProgressValue = ((sheetIndex * 100.0) / sheetCount + p) / sheetCount;
                                    int totalProgress = (int)(fileProgress + sheetProgressValue / filePaths.Length);
                                    progress.Report((i, filePaths.Length, totalProgress));
                                });
                            }

                            var (success, message, order, needsProductCreation, productCode, suggestedProductName, suggestedSpecification) = await ProcessSheetAsync(sheet, sheetName, fileName, sheetProgress);

                            if (success && order != null)
                            {
                                results.Add(order);
                                successSheets++;
                            }
                            else
                            {
                                errors.Add($"文件 '{fileName}' 工作表 '{sheetName}': {message}");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    errors.Add($"处理文件失败: {fileName}\n{ex.Message}");
                }
            }

            if (results.Count == 0)
            {
                return (false, $"导入失败\n{string.Join("\n", errors)}", results);
            }

            string resultMessage = $"成功导入 {results.Count} 个订单";
            if (errors.Any())
            {
                resultMessage += $"\n失败: {totalSheets - successSheets} 个工作表\n{string.Join("\n", errors)}";
                return (false, resultMessage, results);
            }

            return (true, resultMessage, results);
        }

        public async Task<(bool success, string message, ProductionOrder? order, bool needsProductCreation, string productCode, string suggestedProductName, string suggestedSpecification)> ImportFromSheetAsync(
            string filePath, string sheetName, IProgress<int>? progress = null)
        {
            return await ImportFromSheetInternalAsync(filePath, sheetName, progress);
        }

        public async Task<(bool success, string message, ProductionOrder? order, bool needsProductCreation, string productCode, string suggestedProductName, string suggestedSpecification)> ImportFromSheetAsync(
            string filePath, string sheetName, IProgress<(int progress, string orderNumber, string productCode, string productName, int barcodeIndex, int barcodeCount)>? detailedProgress)
        {
            return await ImportFromSheetInternalAsync(filePath, sheetName, detailedProgress);
        }

        private async Task<(bool success, string message, ProductionOrder? order, bool needsProductCreation, string productCode, string suggestedProductName, string suggestedSpecification)> ImportFromSheetInternalAsync(
            string filePath, string sheetName, object? progress)
        {
            try
            {
                // 验证文件存在
                if (!File.Exists(filePath))
                {
                    return (false, $"文件不存在: {filePath}", null, false, string.Empty, string.Empty, string.Empty);
                }

                try
                {
                    // 读取Excel文件
                    IWorkbook workbook;
                    using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                    {
                        // 根据文件扩展名决定使用哪种工作簿
                        string extension = Path.GetExtension(filePath).ToLower();
                        if (extension == ".xlsx")
                        {
                            workbook = new XSSFWorkbook(fileStream);
                        }
                        else if (extension == ".xls")
                        {
                            workbook = new HSSFWorkbook(fileStream);
                        }
                        else
                        {
                            return (false, $"文件格式不支持: {Path.GetFileName(filePath)}\n请使用 .xlsx 或 .xls 格式的Excel文件", null, false, string.Empty, string.Empty, string.Empty);
                        }
                    }

                    using (workbook)
                    {
                        // 查找指定名称的工作表
                        ISheet sheet = null;
                        for (int i = 0; i < workbook.NumberOfSheets; i++)
                        {
                            if (workbook.GetSheetName(i) == sheetName)
                            {
                                sheet = workbook.GetSheetAt(i);
                                break;
                            }
                        }

                        if (sheet == null)
                        {
                            return (false, $"文件中没有找到名为'{sheetName}'的工作表", null, false, string.Empty, string.Empty, string.Empty);
                        }

                        return await ProcessSheetAsync(sheet, sheetName, Path.GetFileName(filePath), progress);
                    }
                }
                catch (IOException ex)
                {
                    return (false, $"文件访问错误: {Path.GetFileName(filePath)}\n{ex.Message}", null, false, string.Empty, string.Empty, string.Empty);
                }
                catch (Exception ex)
                {
                    return (false, $"读取Excel文件失败: {Path.GetFileName(filePath)}\n{ex.Message}", null, false, string.Empty, string.Empty, string.Empty);
                }
            }
            catch (Exception ex)
            {
                return (false, $"导入失败: {ex.Message}", null, false, string.Empty, string.Empty, string.Empty);
            }
        }

        private async Task<(bool success, string message, ProductionOrder? order, bool needsProductCreation, string productCode, string suggestedProductName, string suggestedSpecification)> ProcessSheetAsync(
            ISheet sheet, string sheetName, string fileName, object? progress = null)
        {
            Logging.DebugLogger.Log($"开始处理工作表: '{sheetName}' 来自文件 '{fileName}'");
            try
            {
                // 检查工作表是否为空
                Logging.DebugLogger.Log($"检查工作表数据: 行数={sheet?.PhysicalNumberOfRows ?? 0}");
                if (sheet == null || sheet.PhysicalNumberOfRows <= 1) // 至少需要标题行和一行数据
                {
                    return (false, $"工作表 '{sheetName}' 中没有数据", null, false, string.Empty, string.Empty, string.Empty);
                }

                // 获取标题行
                IRow headerRow = sheet.GetRow(0);
                if (headerRow == null)
                {
                    return (false, $"工作表 '{sheetName}' 中没有标题行", null, false, string.Empty, string.Empty, string.Empty);
                }

                // 查找必要的列
                int omsOrderNumberColumnIndex = -1;
                int productCodeColumnIndex = -1;
                int productNameColumnIndex = -1;
                int barcodeColumnIndex = -1;

                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    ICell cell = headerRow.GetCell(i);
                    if (cell == null) continue;

                    string cellValue = cell.ToString().Trim();
                    if (cellValue == "OMS单号")
                    {
                        omsOrderNumberColumnIndex = i;
                    }
                    else if (cellValue == "型号代码")
                    {
                        productCodeColumnIndex = i;
                    }
                    else if (cellValue == "型号名称")
                    {
                        productNameColumnIndex = i;
                    }
                    else if (cellValue == "条码")
                    {
                        barcodeColumnIndex = i;
                    }
                }

                // 验证必要列是否存在
                if (omsOrderNumberColumnIndex == -1)
                {
                    return (false, $"工作表 '{sheetName}' 中没有找到 'OMS单号' 列", null, false, string.Empty, string.Empty, string.Empty);
                }
                if (productCodeColumnIndex == -1)
                {
                    return (false, $"工作表 '{sheetName}' 中没有找到 '型号代码' 列", null, false, string.Empty, string.Empty, string.Empty);
                }
                if (productNameColumnIndex == -1)
                {
                    return (false, $"工作表 '{sheetName}' 中没有找到 '型号名称' 列", null, false, string.Empty, string.Empty, string.Empty);
                }
                if (barcodeColumnIndex == -1)
                {
                    return (false, $"工作表 '{sheetName}' 中没有找到 '条码' 列", null, false, string.Empty, string.Empty, string.Empty);
                }

                // 获取第一行数据作为订单信息
                IRow firstDataRow = sheet.GetRow(1);
                if (firstDataRow == null)
                {
                    return (false, $"工作表 '{sheetName}' 中没有数据行", null, false, string.Empty, string.Empty, string.Empty);
                }

                // 提取订单信息
                string orderNumber = GetCellValueAsString(firstDataRow.GetCell(omsOrderNumberColumnIndex));
                string productCode = GetCellValueAsString(firstDataRow.GetCell(productCodeColumnIndex));
                string productName = GetCellValueAsString(firstDataRow.GetCell(productNameColumnIndex));

                if (string.IsNullOrWhiteSpace(orderNumber))
                {
                    return (false, $"工作表 '{sheetName}' 中 'OMS单号' 不能为空", null, false, string.Empty, string.Empty, string.Empty);
                }
                if (string.IsNullOrWhiteSpace(productCode))
                {
                    return (false, $"工作表 '{sheetName}' 中 '型号代码' 不能为空", null, false, string.Empty, string.Empty, string.Empty);
                }

                // 验证产品是否存在
                var product = await _productService.GetByCodeAsync(productCode);
                if (product == null)
                {
                    // 产品不存在，需要创建产品
                    // 返回需要创建产品的信息
                    // 使用型号名称列的值作为产品规格
                    return (false, $"产品编码 '{productCode}' 不存在，需要创建新产品", null, true, productCode, sheetName, productName);
                }

                // 检查订单号+产品编码的唯一性
                var existingOrder = await _orderRepository.GetByOrderNumberAndProductCodeAsync(orderNumber, productCode);
                if (existingOrder != null)
                {
                    // 检查是否有已投产的条码
                    bool hasProducedBarcodes = await _barcodeRepository.HasProducedBarcodesAsync(existingOrder.Id);
                    if (hasProducedBarcodes)
                    {
                        return (false, $"订单 '{orderNumber}' 与产品 '{productCode}' 的组合已存在，且已经投产，无法替换", null, false, string.Empty, string.Empty, string.Empty);
                    }

                    // 删除原有条码
                    await _barcodeRepository.DeleteByOrderIdAsync(existingOrder.Id);
                }

                // 读取所有条码
                var barcodes = new List<string>();
                int lastRowNum = sheet.LastRowNum;

                // 初始化进度报告
                var standardProgress = progress as IProgress<int>;
                var detailedProgress = progress as IProgress<(int progress, string orderNumber, string productCode, string productName, int barcodeIndex, int barcodeCount)>;

                // 报告初始状态
                if (detailedProgress != null)
                {
                    detailedProgress.Report((0, orderNumber, productCode, productName, 0, lastRowNum));
                }
                else if (standardProgress != null)
                {
                    standardProgress.Report(0);
                }

                for (int rowIndex = 1; rowIndex <= lastRowNum; rowIndex++) // 从第二行开始（索引为1）
                {
                    IRow row = sheet.GetRow(rowIndex);
                    if (row == null) continue;

                    ICell cell = row.GetCell(barcodeColumnIndex);
                    if (cell == null) continue;

                    string barcode = cell.ToString()?.Trim() ?? string.Empty;
                    if (!string.IsNullOrEmpty(barcode))
                    {
                        barcodes.Add(barcode);
                    }

                    // 更新进度
                    if (lastRowNum > 1) // 防止除零错误
                    {
                        int progressValue = (int)((rowIndex - 1) * 100.0 / (lastRowNum - 1));

                        // 报告进度
                        if (detailedProgress != null)
                        {
                            detailedProgress.Report((progressValue, orderNumber, productCode, productName, rowIndex, lastRowNum));
                        }
                        else if (standardProgress != null)
                        {
                            standardProgress.Report(progressValue);
                        }
                    }
                }

                if (barcodes.Count == 0)
                {
                    return (false, $"工作表 '{sheetName}' 中没有找到有效的条码数据", null, false, string.Empty, string.Empty, string.Empty);
                }

                // 验证是否有重复条码
                var duplicateBarcodes = barcodes.GroupBy(b => b)
                    .Where(g => g.Count() > 1)
                    .Select(g => g.Key)
                    .ToList();

                if (duplicateBarcodes.Any())
                {
                    return (false, $"工作表 '{sheetName}' 中存在重复条码: {string.Join(", ", duplicateBarcodes)}", null, false, string.Empty, string.Empty, string.Empty);
                }

                // 移除每个条码单独验重的逻辑
                // 对于已存在的订单号+产品编码组合，已经在前面检查了是否有已投产的条码
                // 如果没有已投产的条码，则已经删除了原有条码，可以直接导入新的条码

                // 创建或更新订单
                ProductionOrder order;
                try
                {
                    // 使用型号名称作为产品型号
                    string productSpecification = productName; // 使用型号名称列的值作为产品型号

                    if (existingOrder != null)
                    {
                        // 更新现有订单
                        existingOrder.ProductName = product.Name;
                        existingOrder.ProductSpecification = productSpecification; // 使用型号名称作为产品型号
                        existingOrder.Quantity = barcodes.Count;
                        existingOrder.CreatedAt = DateTime.Now;

                        order = await _orderRepository.UpdateAsync(existingOrder);
                    }
                    else
                    {
                        // 创建新订单
                        order = new ProductionOrder
                        {
                            OrderNumber = orderNumber,
                            ProductCode = productCode,
                            ProductName = product.Name,
                            ProductSpecification = productSpecification, // 使用型号名称作为产品型号
                            Quantity = barcodes.Count,
                            CreatedAt = DateTime.Now
                        };

                        order = await _orderRepository.AddAsync(order);
                    }

                    // 创建条码记录
                    var barcodeEntities = barcodes.Select(b => new ProductionOrderBarcode
                    {
                        OrderId = order.Id,
                        Barcode = b,
                        IsProduced = false,
                        CreatedAt = DateTime.Now
                    }).ToList();

                    // 保存条码
                    var savedBarcodes = await _barcodeRepository.AddRangeAsync(barcodeEntities);

                    // 验证是否成功保存
                    if (savedBarcodes == null || savedBarcodes.Count == 0)
                    {
                        return (false, $"保存条码失败: 没有条码被保存", null, false, string.Empty, string.Empty, string.Empty);
                    }

                    // 验证订单是否存在
                    var savedOrder = await _orderRepository.GetByIdAsync(order.Id);
                    if (savedOrder == null)
                    {
                        return (false, $"保存订单失败: 无法找到刚刚创建的订单", null, false, string.Empty, string.Empty, string.Empty);
                    }
                }
                catch (Exception ex)
                {
                    Logging.DebugLogger.LogException("保存订单或条码失败", ex);
                    return (false, $"保存订单或条码失败: {ex.Message}", null, false, string.Empty, string.Empty, string.Empty);
                }

                return (true, $"成功导入 {barcodes.Count} 个条码", order, false, string.Empty, string.Empty, string.Empty);
            }
            catch (Exception ex)
            {
                Logging.DebugLogger.LogException($"导入工作表 '{sheetName}' 失败", ex);
                return (false, $"导入工作表 '{sheetName}' 失败: {ex.Message}", null, false, string.Empty, string.Empty, string.Empty);
            }
        }

        private static string GetCellValueAsString(ICell cell)
        {
            if (cell == null) return string.Empty;

            switch (cell.CellType)
            {
                case CellType.Numeric:
                    // 如果是日期格式
                    if (DateUtil.IsCellDateFormatted(cell))
                    {
                        return cell.DateCellValue.ToString("yyyy-MM-dd");
                    }
                    // 如果是数字，转换为字符串
                    return cell.NumericCellValue.ToString();
                case CellType.String:
                    return cell.StringCellValue;
                case CellType.Boolean:
                    return cell.BooleanCellValue.ToString();
                case CellType.Formula:
                    try
                    {
                        // 尝试获取公式计算结果
                        return cell.StringCellValue;
                    }
                    catch
                    {
                        try
                        {
                            return cell.NumericCellValue.ToString();
                        }
                        catch
                        {
                            return cell.CellFormula;
                        }
                    }
                default:
                    return string.Empty;
            }
        }

        public async Task<ProductionOrder?> GetOrderByIdAsync(int id)
        {
            try
            {
                return await _orderRepository.GetByIdAsync(id);
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> DeleteOrderAsync(int id)
        {
            try
            {
                // 删除订单会级联删除相关的条码记录
                await _orderRepository.DeleteAsync(id);
                return true;
            }
            catch
            {
                return false;
            }
        }
        public async Task<List<ProductionOrder>> GetUncompletedOrdersAsync()
        {
            return await _orderRepository.GetUncompletedOrdersAsync();
        }

        public async Task<int> GetCompletedQuantityAsync(int orderId)
        {
            return await _orderRepository.GetCompletedQuantityAsync(orderId);
        }

        public async Task UpdateOrderFinishedStatusAsync(int orderId)
        {
            await _orderRepository.UpdateOrderFinishedStatusAsync(orderId);
        }

        public async Task MarkBarcodeAsProducedAsync(string barcode)
        {
            // 查找条码
            var barcodeEntity = await _barcodeRepository.GetByBarcodeAsync(barcode);
            if (barcodeEntity != null)
            {
                // 标记条码为已生产
                barcodeEntity.IsProduced = true;
                await _barcodeRepository.UpdateAsync(barcodeEntity);

                // 更新订单完成状态
                await UpdateOrderFinishedStatusAsync(barcodeEntity.OrderId);
            }
        }
        public async Task<bool> IsBarcodeInspectedAsync(string barcode)
        {
            // 检查条码是否已经完成质量检测
            try
            {
                return await _qualityInspectionRepository.ExistsByBarcodeAsync(barcode);
            }
            catch (Exception)
            {
                // 发生异常时返回false，表示未检测
                return false;
            }
        }

        /// <summary>
        /// 获取条码的质量检测状态
        /// </summary>
        /// <param name="barcode">条码</param>
        /// <returns>null=未检测, true=合格, false=不合格</returns>
        public async Task<bool?> GetBarcodeQualityStatusAsync(string barcode)
        {
            try
            {
                var inspections = await _qualityInspectionRepository.GetByBarcodeAsync(barcode);
                // 获取最新的检测记录
                var latestInspection = inspections?.FirstOrDefault();
                return latestInspection?.Result;
            }
            catch (Exception)
            {
                // 发生异常时返回null，表示未检测
                return null;
            }
        }

        public async Task SaveQualityInspectionAsync(
            string barcode,
            int orderId,
            int productId,
            string productCategory,
            bool result,
            string diStatusJson,
            int operatorId,
            string remarks)
        {
            try
            {
                // 每次检测都创建新记录，保留完整的检测历史
                var inspection = new QualityInspection
                {
                    Barcode = barcode,
                    OrderId = orderId,
                    ProductId = productId,
                    ProductCategory = productCategory,
                    Result = result,
                    DiStatusJson = diStatusJson,
                    OperatorId = operatorId,
                    Remarks = remarks ?? string.Empty,
                    CreatedAt = DateTime.Now
                };

                // 保存到数据库
                await _qualityInspectionRepository.AddAsync(inspection);
            }
            catch (Exception ex)
            {
                // 记录异常，但不抛出，以免影响UI操作
                Console.WriteLine($"保存质量检测记录失败: {ex.Message}");
            }
        }

        public async Task UpdateQualityInspectionResultAsync(string barcode, bool result, string diStatusJson = null)
        {
            try
            {
                // 获取该条码的最新不合格记录
                var inspections = await _qualityInspectionRepository.GetByBarcodeAsync(barcode);
                var latestFailedInspection = inspections
                    .Where(i => !i.Result) // 只查找不合格的记录
                    .OrderByDescending(i => i.CreatedAt)
                    .FirstOrDefault();

                if (latestFailedInspection != null)
                {
                    // 更新最新的不合格记录为合格
                    latestFailedInspection.Result = result;
                    if (!string.IsNullOrEmpty(diStatusJson))
                    {
                        latestFailedInspection.DiStatusJson = diStatusJson;
                    }
                    latestFailedInspection.CreatedAt = DateTime.Now;

                    await _qualityInspectionRepository.UpdateAsync(latestFailedInspection);
                }
                else
                {
                    // 如果没有找到不合格记录，记录警告但不抛出异常
                    Console.WriteLine($"警告：条码 {barcode} 没有找到可更新的不合格记录");
                }
            }
            catch (Exception ex)
            {
                // 记录异常，但不抛出，以免影响UI操作
                Console.WriteLine($"更新质量检测记录失败: {ex.Message}");
            }
        }

        public async Task<bool> HasQualityPassedAsync(string barcode)
        {
            try
            {
                // 查询该条码的质量检测记录
                var inspections = await _qualityInspectionRepository.GetByBarcodeAsync(barcode);

                // 检查是否有合格的质量检测记录
                return inspections.Any(i => i.Result == true);
            }
            catch (Exception)
            {
                // 发生异常时返回false
                return false;
            }
        }

        public async Task SaveProductPhotoAsync(string barcode, int orderId, string photoPath, int operatorId)
        {
            try
            {
                Console.WriteLine($"开始保存成品拍照记录: 条码={barcode}, 订单ID={orderId}, 照片路径={photoPath}, 操作员ID={operatorId}");

                // 创建成品拍照记录
                var photo = new ProductPhoto
                {
                    Barcode = barcode,
                    OrderId = orderId,
                    PhotoPath = photoPath,
                    OperatorId = operatorId,
                    CreatedAt = DateTime.Now
                };

                Console.WriteLine($"创建ProductPhoto对象成功，准备保存到数据库...");

                // 保存到数据库
                var savedPhoto = await _productPhotoRepository.AddAsync(photo);

                Console.WriteLine($"成品拍照记录保存成功: ID={savedPhoto.Id}, 条码={savedPhoto.Barcode}");

                // 注意：这里不再标记为已生产，而是等待小盒贴打印工序完成后再标记
            }
            catch (Exception ex)
            {
                // 记录详细异常信息并抛出，让UI层能够感知到错误
                Console.WriteLine($"保存成品拍照记录失败: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                throw; // 抛出异常，让调用方知道保存失败
            }
        }

        public async Task<bool> HasProductPhotoAsync(string barcode)
        {
            try
            {
                // 使用注入的Repository检查是否存在该条码的拍照记录
                return await _productPhotoRepository.ExistsByBarcodeAsync(barcode);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查条码拍照记录时发生异常: 条码={barcode}, 异常={ex}");
                throw;
            }
        }

        public async Task<string> GenerateBoxNumberAsync()
        {
            try
            {
                return await _boxLabelSequenceRepository.GenerateNextBoxNumberAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成盒号失败: {ex.Message}");
                // 如果生成失败，返回一个临时盒号
                return DateTime.Now.ToString("yyMMddHHmmss");
            }
        }

        public async Task SaveBoxPackageAsync(string boxNumber, int orderId, int productId, List<string> barcodes, int operatorId)
        {
            try
            {
                // 创建盒装记录
                var boxPackage = new BoxPackage
                {
                    BoxNumber = boxNumber,
                    OrderId = orderId,
                    ProductId = productId,
                    BarcodeCount = barcodes.Count,
                    OperatorId = operatorId,
                    CreatedAt = DateTime.Now
                };

                // 保存盒装记录
                await _boxPackageRepository.AddAsync(boxPackage);

                // 更新条码表，设置盒号并标记为已生产
                foreach (var barcode in barcodes)
                {
                    var barcodeEntity = await _barcodeRepository.GetByBarcodeAsync(barcode);
                    if (barcodeEntity != null)
                    {
                        barcodeEntity.BoxNumber = boxNumber;
                        barcodeEntity.IsProduced = true; // 完成小盒贴打印后才标记为已生产
                        await _barcodeRepository.UpdateAsync(barcodeEntity);
                    }
                }

                // 更新订单完成状态
                await UpdateOrderFinishedStatusAsync(orderId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存盒装记录失败: {ex.Message}");
                throw; // 这里抛出异常，让调用方知道保存失败
            }
        }

        public async Task<string> GenerateCartonNumberAsync()
        {
            try
            {
                // 生成日期码（YYMMDD）
                string dateCode = DateTime.Now.ToString("yyMMdd");

                // 获取下一个序号（4位）
                int sequence = await _cartonLabelSequenceRepository.GetNextSequenceAsync(dateCode);

                // 组合成箱号：YYMMDD + 4位序号
                return $"{dateCode}{sequence:D4}";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成箱号失败: {ex.Message}");
                // 如果生成失败，返回一个临时箱号
                return DateTime.Now.ToString("yyMMddHHmm");
            }
        }

        public async Task<bool> SaveCartonPackageAsync(string cartonNumber, int orderId, int productId, List<string> boxNumbers, decimal weight, int operatorId)
        {
            try
            {
                // 计算总产品数量
                int totalQuantity = 0;
                foreach (var boxNumber in boxNumbers)
                {
                    var boxPackage = await _boxPackageRepository.GetByBoxNumberAsync(boxNumber);
                    if (boxPackage != null)
                    {
                        totalQuantity += boxPackage.BarcodeCount;
                    }
                }

                // 创建大箱包装记录
                var cartonPackage = new CartonPackage
                {
                    CartonNumber = cartonNumber,
                    OrderId = orderId,
                    ProductId = productId,
                    Weight = weight,
                    TotalQuantity = totalQuantity,
                    BoxCount = boxNumbers.Count,
                    ProductionDate = DateTime.Today,
                    OperatorId = operatorId,
                    CreatedAt = DateTime.Now
                };

                // 保存大箱记录
                await _cartonPackageRepository.SaveAsync(cartonPackage);

                // 保存大箱与小盒的关联关系
                await _cartonPackageRepository.SaveCartonBoxMappingsAsync(cartonNumber, boxNumbers);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存大箱记录失败: {ex.Message}");
                return false;
            }
        }

        public async Task<List<string>> GetCartonQueueBoxNumbersAsync(int orderId, int count = 2)
        {
            try
            {
                // 从大箱队列表中获取盒号
                return await _cartonQueueRepository.GetBoxNumbersByOrderIdAsync(orderId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取大箱队列失败: {ex.Message}");
                return new List<string>();
            }
        }

        private async Task<List<string>> GetUsedBoxNumbersAsync()
        {
            try
            {
                // 获取所有已经装箱的盒号（在CartonBoxMapping表中的盒号）
                return await _cartonQueueRepository.GetUsedBoxNumbersAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取已装箱盒号失败: {ex.Message}");
                return new List<string>();
            }
        }

        public async Task<List<string>> GetBarcodesByBoxNumberAsync(string boxNumber)
        {
            try
            {
                return await _barcodeRepository.GetBarcodesByBoxNumberAsync(boxNumber);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取盒号条码失败: {ex.Message}");
                return new List<string>();
            }
        }

        #region 小盒队列相关方法

        /// <summary>
        /// 添加条码到小盒队列
        /// </summary>
        public async Task AddBarcodeToBoxQueueAsync(string barcode, int orderId, int productId)
        {
            try
            {
                var boxQueue = new BoxQueue
                {
                    Barcode = barcode,
                    OrderId = orderId,
                    ProductId = productId,
                    CreatedAt = DateTime.Now
                };

                await _boxQueueRepository.AddAsync(boxQueue);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加条码到小盒队列失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取订单的小盒队列条码列表
        /// </summary>
        public async Task<List<string>> GetBoxQueueBarcodesAsync(int orderId)
        {
            try
            {
                return await _boxQueueRepository.GetBarcodesByOrderIdAsync(orderId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取小盒队列条码失败: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// 从小盒队列中删除条码
        /// </summary>
        public async Task RemoveBarcodesFromBoxQueueAsync(List<string> barcodes)
        {
            try
            {
                await _boxQueueRepository.DeleteByBarcodesAsync(barcodes);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"从小盒队列删除条码失败: {ex.Message}");
                throw;
            }
        }
        public async Task<string?> GetBoxNumberByBarCode(string barcode)
        {
            try
            {
                return await _barcodeRepository.GetBoxNumberByBarCodeAsync(barcode);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取盒号条码失败: {ex.Message}");
                return string.Empty;
            }
        }
        #endregion

        #region 大箱队列相关方法

        /// <summary>
        /// 添加盒号到大箱队列
        /// </summary>
        public async Task AddBoxToCartonQueueAsync(string boxNumber, int orderId, int productId)
        {
            try
            {
                // 检查盒号是否已在队列中
                bool exists = await _cartonQueueRepository.ExistsByBoxNumberAsync(boxNumber);
                if (exists)
                {
                    Console.WriteLine($"盒号 {boxNumber} 已在大箱队列中");
                    return;
                }

                var cartonQueue = new CartonQueue
                {
                    BoxNumber = boxNumber,
                    OrderId = orderId,
                    ProductId = productId,
                    CreatedAt = DateTime.Now
                };

                await _cartonQueueRepository.AddAsync(cartonQueue);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加盒号到大箱队列失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取订单的大箱队列盒号列表
        /// </summary>
        public async Task<List<string>> GetCartonQueueBoxNumbersAsync(int orderId)
        {
            try
            {
                return await _cartonQueueRepository.GetBoxNumbersByOrderIdAsync(orderId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取大箱队列盒号失败: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// 从大箱队列中删除盒号
        /// </summary>
        public async Task RemoveBoxNumbersFromCartonQueueAsync(List<string> boxNumbers)
        {
            try
            {
                await _cartonQueueRepository.DeleteByBoxNumbersAsync(boxNumbers);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"从大箱队列删除盒号失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取指定数量的盒号用于装箱
        /// </summary>
        public async Task<List<string>> GetBoxNumbersForCartonPackingAsync(int orderId, int count = 2)
        {
            try
            {
                return await _cartonQueueRepository.GetBoxNumbersForPackingAsync(orderId, count);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取装箱盒号失败: {ex.Message}");
                return new List<string>();
            }
        }

        #endregion
    }

}
