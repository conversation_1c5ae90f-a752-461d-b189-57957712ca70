using OutletProductionPacking.Data.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OutletProductionPacking.Data.Repositories
{
    public interface IProductPhotoRepository
    {
        Task<ProductPhoto> GetByIdAsync(int id);
        Task<List<ProductPhoto>> GetByBarcodeAsync(string barcode);
        Task<bool> ExistsByBarcodeAsync(string barcode);
        Task<ProductPhoto> AddAsync(ProductPhoto photo);
        Task<List<ProductPhoto>> GetByOrderIdAsync(int orderId);
        Task<List<ProductPhoto>> GetRecentPhotosAsync(int count = 100);
        Task<List<ProductPhotoWithOrderInfo>> GetRecentPhotosWithOrderInfoAsync(int count = 100);
        Task<List<ProductPhotoWithOrderInfo>> GetRecentPhotosByOrderIdAsync(int orderId, int count = 100);
        Task<List<ProductPhoto>> GetUnwatermarkedPhotosAsync(int count = 100);
        Task UpdateWatermarkStatusAsync(int id, short status);
    }
}
