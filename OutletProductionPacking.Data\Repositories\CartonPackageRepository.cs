using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public class CartonPackageRepository : ICartonPackageRepository
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public CartonPackageRepository(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<CartonPackage> SaveAsync(CartonPackage cartonPackage)
        {
            using var context = _contextFactory.CreateDbContext();

            context.CartonPackages.Add(cartonPackage);
            await context.SaveChangesAsync();

            return cartonPackage;
        }

        public async Task<CartonPackage?> GetByCartonNumberAsync(string cartonNumber)
        {
            using var context = _contextFactory.CreateDbContext();

            return await context.CartonPackages
                .Include(c => c.Order)
                .Include(c => c.Product)
                .Include(c => c.Operator)
                .Include(c => c.CartonBoxMappings)
                .FirstOrDefaultAsync(c => c.CartonNumber == cartonNumber);
        }

        public async Task<List<CartonPackage>> GetByOrderIdAsync(int orderId)
        {
            using var context = _contextFactory.CreateDbContext();

            return await context.CartonPackages
                .Include(c => c.Order)
                .Include(c => c.Product)
                .Include(c => c.Operator)
                .Where(c => c.OrderId == orderId)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();
        }

        public async Task<bool> SaveCartonBoxMappingsAsync(string cartonNumber, List<string> boxNumbers)
        {
            using var context = _contextFactory.CreateDbContext();

            try
            {
                // 先查找大箱记录
                var cartonPackage = await context.CartonPackages
                    .FirstOrDefaultAsync(c => c.CartonNumber == cartonNumber);

                if (cartonPackage == null)
                {
                    return false; // 大箱记录不存在
                }

                var mappings = boxNumbers.Select(boxNumber => new CartonBoxMapping
                {
                    CartonPackageId = cartonPackage.Id,
                    CartonNumber = cartonNumber,
                    BoxNumber = boxNumber,
                    CreatedAt = DateTime.Now
                }).ToList();

                context.CartonBoxMappings.AddRange(mappings);
                await context.SaveChangesAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<string>> GetBoxNumbersByCartonAsync(string cartonNumber)
        {
            using var context = _contextFactory.CreateDbContext();

            return await context.CartonBoxMappings
                .Where(m => m.CartonNumber == cartonNumber)
                .Select(m => m.BoxNumber)
                .ToListAsync();
        }

        public async Task<string> GetCartonNumberByBoxNumberAsync(string boxNumber)
        {
            using var context = _contextFactory.CreateDbContext();

            var mapping = await context.CartonBoxMappings
                .Where(m => m.BoxNumber == boxNumber)
                .FirstOrDefaultAsync();

            return mapping?.CartonNumber ?? string.Empty;
        }
    }
}
