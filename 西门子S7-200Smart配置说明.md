# 西门子S7-200 Smart PLC Modbus配置说明

## 📋 概述

本文档说明如何配置西门子S7-200 Smart PLC的Modbus TCP功能，以及如何在现有程序中读写V0.0等V区地址。

## 🔧 PLC端配置

### 1. 硬件要求
- 西门子S7-200 Smart CPU（支持以太网通信）
- 以太网线缆
- 与PC在同一网段

### 2. PLC程序配置

#### 2.1 启用Modbus TCP服务
在STEP 7-Micro/WIN SMART中：

1. 打开项目，进入**系统块**
2. 选择**通信**选项卡
3. 勾选**启用Modbus TCP服务器**
4. 设置**端口号**为 `502`（默认）
5. 设置**连接数**（建议1-4个）

#### 2.2 网络配置
1. 进入**以太网**配置
2. 设置PLC的IP地址，例如：`************`
3. 设置子网掩码：`*************`
4. 确保与PC在同一网段

#### 2.3 V区地址映射
西门子S7-200 Smart的V区地址映射到Modbus保持寄存器：

| V区地址 | Modbus寄存器地址 | 说明 |
|---------|------------------|------|
| V0.0    | 0               | 第1个字节 |
| V0.1    | 0               | 第1个字节（高8位） |
| V1.0    | 1               | 第2个字节 |
| V2.0    | 2               | 第3个字节 |
| VW0     | 0               | 第1个字（16位） |
| VW2     | 2               | 第2个字（16位） |

## 💻 程序端配置

### 1. 修改配置文件

编辑 `appsettings.json`：

```json
{
  "Hardware": {
    "Modbus": {
      "IP": "************",
      "Port": 502
    }
  }
}
```

### 2. 使用新的API方法

#### 2.1 基本连接
```csharp
var modbusService = new ModbusService();
bool connected = await modbusService.ConnectAsync("************", 502);
```

#### 2.2 读写V0.0字节值
```csharp
// 写入V0.0 = 100
await modbusService.WriteVByteAsync(0, 100);

// 读取V0.0
byte value = await modbusService.ReadVByteAsync(0);
```

#### 2.3 读写V0.0字值（16位）
```csharp
// 写入VW0 = 12345
await modbusService.WriteVWordAsync(0, 12345);

// 读取VW0
ushort value = await modbusService.ReadVWordAsync(0);
```

#### 2.4 读写V0.0的位
```csharp
// 设置V0.0.0位为True
await modbusService.WriteVBitAsync(0, 0, true);

// 读取V0.0.7位
bool bit7 = await modbusService.ReadVBitAsync(0, 7);
```

#### 2.5 批量操作
```csharp
// 批量读取V0.0-V9.1（10个字）
ushort[] values = await modbusService.ReadHoldingRegistersAsync(0, 10);

// 批量写入
ushort[] writeValues = {1000, 2000, 3000};
await modbusService.WriteHoldingRegistersAsync(0, writeValues);
```

## 🧪 测试方法

### 1. 快速测试
```csharp
// 在Program.cs或测试方法中调用
await S7TestProgram.QuickTestV0Async("************");
```

### 2. 完整测试
```csharp
await S7TestProgram.RunTestAsync();
```

### 3. 生产应用示例
```csharp
var modbusService = new ModbusService();
var s7Example = new S7ModbusExample(modbusService);

await s7Example.ConnectToPLC("************");
await s7Example.ProductionExample();
```

## 🔍 常见问题排查

### 1. 连接失败
- 检查PLC的IP地址是否正确
- 确认PLC已启用Modbus TCP服务
- 检查防火墙设置
- 确认网络连通性（ping测试）

### 2. 读写失败
- 检查V区地址是否在有效范围内
- 确认PLC程序中V区已分配
- 检查数据类型是否匹配

### 3. 性能问题
- 减少读写频率
- 使用批量操作代替单个操作
- 检查网络延迟

## 📊 地址对照表

### V区字节地址对照
| V区地址 | Modbus地址 | 字节位置 |
|---------|------------|----------|
| V0.0    | 0          | 低字节   |
| V0.1    | 0          | 高字节   |
| V1.0    | 1          | 低字节   |
| V1.1    | 1          | 高字节   |
| V2.0    | 2          | 低字节   |
| V2.1    | 2          | 高字节   |

### V区字地址对照
| V区地址 | Modbus地址 | 数据类型 |
|---------|------------|----------|
| VW0     | 0          | 16位字   |
| VW2     | 2          | 16位字   |
| VW4     | 4          | 16位字   |
| VW6     | 6          | 16位字   |

### V区位地址对照
| V区地址   | Modbus地址 | 字节地址 | 位索引 |
|-----------|------------|----------|--------|
| V0.0.0    | 0          | V0.0     | 0      |
| V0.0.1    | 0          | V0.0     | 1      |
| V0.0.7    | 0          | V0.0     | 7      |
| V1.0.0    | 1          | V1.0     | 0      |

## 🚀 实际应用建议

### 1. 生产控制
- V0.0-V9.1：设备控制位
- V10.0-V19.1：传感器状态
- V20.0-V29.1：生产参数
- V30.0-V39.1：计数器值

### 2. 数据组织
- 按功能分组分配V区地址
- 预留扩展空间
- 建立地址映射表
- 定期备份配置

### 3. 错误处理
- 实现连接重试机制
- 添加超时处理
- 记录操作日志
- 提供状态监控

## 📝 注意事项

1. **地址范围**：确保V区地址在PLC支持范围内
2. **数据类型**：注意字节序和数据对齐
3. **并发访问**：避免同时读写同一地址
4. **性能优化**：合理使用批量操作
5. **错误处理**：实现完善的异常处理机制
