﻿namespace OutletProductionPacking.Core.Services
{
    public interface IModbusService
    {
        Task<bool> ConnectAsync(string ipAddress, int port = 502);
        void Disconnect();
        Task<bool[]> ReadInputsAsync(ushort startAddress, ushort count);
        Task<bool> WriteCoilAsync(ushort coilAddress, bool value);
        Task<bool> WriteRegisterAsync(ushort coilAddress, ushort value);
        Task WriteCoilsAsync(ushort startAddress, bool[] values);
        bool IsConnected { get; }

        // 新增：西门子S7-200 Smart V区读写方法
        Task<ushort[]> ReadHoldingRegistersAsync(ushort startAddress, ushort count);
        Task WriteHoldingRegistersAsync(ushort startAddress, ushort[] values);
        Task<byte> ReadVByteAsync(ushort vAddress);
        Task WriteVByteAsync(ushort vAddress, byte value);
        Task<ushort> ReadVWordAsync(ushort vAddress);
        Task WriteVWordAsync(ushort vAddress, ushort value);
        Task<bool> ReadVBitAsync(ushort vAddress, byte bitIndex);
        Task WriteVBitAsync(ushort vAddress, byte bitIndex, bool value);
    }
}