# 打印服务守护功能说明

## 📋 功能概述

实现了一个简单的打印服务守护线程，自动管理BarTenderPrintService的运行。

## 🚀 功能特性

### 1. **自动启动**
- 主软件启动时，自动启动BarTenderPrintService
- 如果BarTenderPrintService文件夹不存在，则忽略所有操作

### 2. **守护监控**
- 创建守护线程，每5秒检查一次打印服务是否在运行
- 如果发现服务被关闭，自动重新启动

### 3. **自动关闭**
- 主软件关闭时，自动停止BarTenderPrintService
- 先尝试优雅关闭，3秒后强制终止

## 📁 文件结构

```
应用程序根目录/
├── OutletProductionPacking.exe        # 主程序
├── BarTenderPrintService/              # 打印服务文件夹
│   └── BarTenderPrintService.exe       # 打印服务程序
└── ...其他文件
```

## 🔧 技术实现

### 核心文件
- **PrintServiceDaemon.cs** - 打印服务守护者类
- **App.xaml.cs** - 集成守护者到应用程序生命周期

### 工作原理
1. 应用程序启动时检查 `BarTenderPrintService` 文件夹是否存在
2. 如果存在，创建守护线程并启动打印服务
3. 守护线程每5秒检查一次打印服务进程状态
4. 如果进程不存在或已退出，自动重新启动
5. 应用程序关闭时，停止守护线程并终止打印服务

## 📝 使用方法

### 部署步骤
1. 将编译好的BarTenderPrintService程序放在主程序根目录下的 `BarTenderPrintService` 文件夹中
2. 确保 `BarTenderPrintService.exe` 文件存在
3. 正常启动主程序

### 运行效果
- ✅ 主程序启动 → 自动启动打印服务（显示控制台窗口）
- ✅ 打印服务异常退出 → 5秒内自动重启
- ✅ 主程序关闭 → 自动停止打印服务
- ✅ 可以看到打印服务的控制台日志输出

## 🔍 日志记录

所有操作都会记录在应用程序日志中：
- 守护者启动/停止
- 打印服务启动/停止
- 异常情况处理

## ⚠️ 注意事项

1. **文件夹检查**：如果没有 `BarTenderPrintService` 文件夹，守护者不会启动
2. **控制台显示**：打印服务会显示控制台窗口，可以查看实时日志
3. **进程管理**：确保不会产生僵尸进程
4. **异常处理**：所有异常都会被捕获并记录日志

## 🎯 优势

- **简单可靠**：逻辑简单，不易出错
- **自动恢复**：服务异常退出时自动重启
- **无感知**：用户无需手动操作
- **资源友好**：守护线程占用资源极少
- **日志可见**：显示控制台窗口，便于查看实时日志和问题排查

这个实现完全满足您的需求：简单、可靠、自动化。
