using OutletProductionPacking.Data.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OutletProductionPacking.Core.Services
{
    /// <summary>
    /// 条码追溯服务接口
    /// </summary>
    public interface IBarcodeTraceService
    {
        /// <summary>
        /// 根据条码获取完整的追溯信息
        /// </summary>
        /// <param name="barcode">条码</param>
        /// <returns>追溯信息，如果条码不存在返回null</returns>
        Task<BarcodeTraceInfo?> GetBarcodeTraceInfoAsync(string barcode);
        
        /// <summary>
        /// 检查条码是否存在
        /// </summary>
        /// <param name="barcode">条码</param>
        /// <returns>是否存在</returns>
        Task<bool> BarcodeExistsAsync(string barcode);
        
        /// <summary>
        /// 获取条码的生产历程
        /// </summary>
        /// <param name="barcode">条码</param>
        /// <returns>生产步骤列表</returns>
        Task<List<ProductionStep>> GetProductionStepsAsync(string barcode);
    }
}
