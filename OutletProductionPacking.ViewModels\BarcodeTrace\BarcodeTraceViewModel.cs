using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Data.Models;
using OutletProductionPacking.Utils.Services;
using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace OutletProductionPacking.ViewModels.BarcodeTrace
{
    /// <summary>
    /// 条码追溯视图模型
    /// </summary>
    public partial class BarcodeTraceViewModel : ObservableObject
    {
        private readonly IBarcodeTraceService _barcodeTraceService;
        private readonly IMessageService _messageService;
        private readonly ILogService _logService;

        public BarcodeTraceViewModel(
            IBarcodeTraceService barcodeTraceService,
            IMessageService messageService,
            ILogService logService)
        {
            _barcodeTraceService = barcodeTraceService;
            _messageService = messageService;
            _logService = logService;
        }

        #region 属性

        /// <summary>
        /// 搜索条码
        /// </summary>
        [ObservableProperty]
        private string _searchBarcode = string.Empty;

        /// <summary>
        /// 追溯信息
        /// </summary>
        [ObservableProperty]
        private BarcodeTraceInfo? _traceInfo;

        /// <summary>
        /// 是否正在搜索
        /// </summary>
        [ObservableProperty]
        private bool _isSearching = false;

        /// <summary>
        /// 是否有结果
        /// </summary>
        [ObservableProperty]
        private bool _hasResult = false;

        /// <summary>
        /// 错误信息
        /// </summary>
        [ObservableProperty]
        private string _errorMessage = string.Empty;

        /// <summary>
        /// 是否有质检信息
        /// </summary>
        public bool HasQualityInfo => TraceInfo?.QualityInfo != null;

        /// <summary>
        /// 是否有拍照信息
        /// </summary>
        public bool HasPhotoInfo => TraceInfo?.PhotoInfo != null;

        /// <summary>
        /// 是否有包装信息
        /// </summary>
        public bool HasPackagingInfo => TraceInfo?.PackageInfo != null;

        /// <summary>
        /// 质检结果显示文本
        /// </summary>
        public string QualityResultText => TraceInfo?.QualityInfo?.Result == true ? "✅ 合格" : "❌ 不合格";

        /// <summary>
        /// 质检结果颜色
        /// </summary>
        public string QualityResultColor => TraceInfo?.QualityInfo?.Result == true ? "Green" : "Red";

        #endregion

        #region 命令

        /// <summary>
        /// 搜索命令
        /// </summary>
        [RelayCommand]
        private async Task SearchAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchBarcode))
            {
                ErrorMessage = "请输入条码";
                return;
            }

            IsSearching = true;
            ErrorMessage = string.Empty;
            HasResult = false;
            TraceInfo = null;

            try
            {
                _logService.Info($"开始查询条码追溯: {SearchBarcode.Trim()}");

                var result = await _barcodeTraceService.GetBarcodeTraceInfoAsync(SearchBarcode.Trim());
                if (result != null)
                {
                    TraceInfo = result;
                    HasResult = true;
                    _logService.Info($"条码追溯查询成功: {SearchBarcode.Trim()}");
                    
                    // 通知相关属性变化
                    OnPropertyChanged(nameof(HasQualityInfo));
                    OnPropertyChanged(nameof(HasPhotoInfo));
                    OnPropertyChanged(nameof(HasPackagingInfo));
                    OnPropertyChanged(nameof(QualityResultText));
                    OnPropertyChanged(nameof(QualityResultColor));
                }
                else
                {
                    ErrorMessage = "未找到该条码的生产记录";
                    _logService.Warn($"条码追溯查询无结果: {SearchBarcode.Trim()}");
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"查询失败: {ex.Message}";
                _logService.Error(ex, $"条码追溯查询失败: {SearchBarcode.Trim()}");
            }
            finally
            {
                IsSearching = false;
            }
        }

        /// <summary>
        /// 清空命令
        /// </summary>
        [RelayCommand]
        private void Clear()
        {
            SearchBarcode = string.Empty;
            TraceInfo = null;
            HasResult = false;
            ErrorMessage = string.Empty;
            
            // 通知相关属性变化
            OnPropertyChanged(nameof(HasQualityInfo));
            OnPropertyChanged(nameof(HasPhotoInfo));
            OnPropertyChanged(nameof(HasPackagingInfo));
            OnPropertyChanged(nameof(QualityResultText));
            OnPropertyChanged(nameof(QualityResultColor));
        }

        /// <summary>
        /// 查看照片命令
        /// </summary>
        [RelayCommand]
        private async Task ViewPhotoAsync()
        {
            try
            {
                if (TraceInfo?.PhotoInfo?.PhotoPath != null && File.Exists(TraceInfo.PhotoInfo.PhotoPath))
                {
                    var startInfo = new ProcessStartInfo
                    {
                        FileName = TraceInfo.PhotoInfo.PhotoPath,
                        UseShellExecute = true
                    };
                    Process.Start(startInfo);
                    _logService.Info($"打开照片文件: {TraceInfo.PhotoInfo.PhotoPath}");
                }
                else
                {
                    _messageService.ShowWarning("照片文件不存在或路径无效");
                    _logService.Warn($"照片文件不存在: {TraceInfo?.PhotoInfo?.PhotoPath}");
                }
            }
            catch (Exception ex)
            {
                _messageService.ShowError($"打开照片失败: {ex.Message}");
                _logService.Error(ex, "打开照片文件失败");
            }
        }

        #endregion

        #region 属性变化通知

        partial void OnTraceInfoChanged(BarcodeTraceInfo? value)
        {
            OnPropertyChanged(nameof(HasQualityInfo));
            OnPropertyChanged(nameof(HasPhotoInfo));
            OnPropertyChanged(nameof(HasPackagingInfo));
            OnPropertyChanged(nameof(QualityResultText));
            OnPropertyChanged(nameof(QualityResultColor));
        }

        #endregion
    }
}
