using System;
using System.Collections.Generic;

namespace OutletProductionPacking.Data.Models
{
    /// <summary>
    /// 条码追溯信息
    /// </summary>
    public class BarcodeTraceInfo
    {
        /// <summary>
        /// 条码
        /// </summary>
        public string Barcode { get; set; } = string.Empty;
        
        /// <summary>
        /// 订单号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;
        
        /// <summary>
        /// 产品型号
        /// </summary>
        public string ProductCode { get; set; } = string.Empty;
        
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; } = string.Empty;
        
        /// <summary>
        /// 产品规格
        /// </summary>
        public string ProductSpec { get; set; } = string.Empty;
        
        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime ProductionDate { get; set; }
        
        /// <summary>
        /// 质检信息
        /// </summary>
        public QualityInspectionInfo? QualityInfo { get; set; }
        
        /// <summary>
        /// 拍照信息
        /// </summary>
        public ProductPhotoInfo? PhotoInfo { get; set; }
        
        /// <summary>
        /// 包装信息
        /// </summary>
        public PackagingInfo? PackageInfo { get; set; }
        
        /// <summary>
        /// 生产历程
        /// </summary>
        public List<ProductionStep> ProductionSteps { get; set; } = new();
    }

    /// <summary>
    /// 质检信息
    /// </summary>
    public class QualityInspectionInfo
    {
        /// <summary>
        /// 质检时间
        /// </summary>
        public DateTime InspectionTime { get; set; }
        
        /// <summary>
        /// 质检结果
        /// </summary>
        public bool Result { get; set; }
        
        /// <summary>
        /// 生产模式
        /// </summary>
        public string ProductionMode { get; set; } = string.Empty;
        
        /// <summary>
        /// DI状态
        /// </summary>
        public string DiStatus { get; set; } = string.Empty;
        
        /// <summary>
        /// 操作员姓名
        /// </summary>
        public string OperatorName { get; set; } = string.Empty;
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; } = string.Empty;
    }

    /// <summary>
    /// 拍照信息
    /// </summary>
    public class ProductPhotoInfo
    {
        /// <summary>
        /// 拍照时间
        /// </summary>
        public DateTime PhotoTime { get; set; }
        
        /// <summary>
        /// 照片路径
        /// </summary>
        public string PhotoPath { get; set; } = string.Empty;
        
        /// <summary>
        /// 生产模式
        /// </summary>
        public string ProductionMode { get; set; } = string.Empty;
        
        /// <summary>
        /// 水印状态
        /// </summary>
        public string WatermarkStatus { get; set; } = string.Empty;
        
        /// <summary>
        /// 操作员姓名
        /// </summary>
        public string OperatorName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 包装信息
    /// </summary>
    public class PackagingInfo
    {
        /// <summary>
        /// 装盒时间
        /// </summary>
        public DateTime? BoxTime { get; set; }
        
        /// <summary>
        /// 盒号
        /// </summary>
        public string BoxNumber { get; set; } = string.Empty;
        
        /// <summary>
        /// 装盒生产模式
        /// </summary>
        public string BoxProductionMode { get; set; } = string.Empty;
        
        /// <summary>
        /// 装箱时间
        /// </summary>
        public DateTime? CartonTime { get; set; }
        
        /// <summary>
        /// 箱号
        /// </summary>
        public string CartonNumber { get; set; } = string.Empty;
        
        /// <summary>
        /// 箱重
        /// </summary>
        public decimal CartonWeight { get; set; }
        
        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalQuantity { get; set; }
        
        /// <summary>
        /// 装箱操作员姓名
        /// </summary>
        public string CartonOperatorName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 生产步骤
    /// </summary>
    public class ProductionStep
    {
        /// <summary>
        /// 步骤时间
        /// </summary>
        public DateTime StepTime { get; set; }
        
        /// <summary>
        /// 步骤名称
        /// </summary>
        public string StepName { get; set; } = string.Empty;
        
        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; } = string.Empty;
        
        /// <summary>
        /// 生产模式
        /// </summary>
        public string ProductionMode { get; set; } = string.Empty;
        
        /// <summary>
        /// 详情
        /// </summary>
        public string Details { get; set; } = string.Empty;
    }
}
